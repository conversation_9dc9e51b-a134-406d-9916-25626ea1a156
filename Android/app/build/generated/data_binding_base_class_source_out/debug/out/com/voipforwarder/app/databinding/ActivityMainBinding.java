// Generated by view binder compiler. Do not edit!
package com.voipforwarder.app.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.switchmaterial.SwitchMaterial;
import com.voipforwarder.app.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final CoordinatorLayout rootView;

  @NonNull
  public final MaterialButton btnCallLogs;

  @NonNull
  public final MaterialButton btnConfiguration;

  @NonNull
  public final MaterialButton btnSettings;

  @NonNull
  public final MaterialCardView cardCurrentCall;

  @NonNull
  public final View statusIndicator;

  @NonNull
  public final SwitchMaterial switchForwarding;

  @NonNull
  public final MaterialToolbar toolbar;

  @NonNull
  public final TextView tvCurrentCallNumber;

  @NonNull
  public final TextView tvCurrentCallStatus;

  @NonNull
  public final TextView tvForwardingStatus;

  @NonNull
  public final TextView tvStatusDetails;

  @NonNull
  public final TextView tvTotalFailed;

  @NonNull
  public final TextView tvTotalForwarded;

  private ActivityMainBinding(@NonNull CoordinatorLayout rootView,
      @NonNull MaterialButton btnCallLogs, @NonNull MaterialButton btnConfiguration,
      @NonNull MaterialButton btnSettings, @NonNull MaterialCardView cardCurrentCall,
      @NonNull View statusIndicator, @NonNull SwitchMaterial switchForwarding,
      @NonNull MaterialToolbar toolbar, @NonNull TextView tvCurrentCallNumber,
      @NonNull TextView tvCurrentCallStatus, @NonNull TextView tvForwardingStatus,
      @NonNull TextView tvStatusDetails, @NonNull TextView tvTotalFailed,
      @NonNull TextView tvTotalForwarded) {
    this.rootView = rootView;
    this.btnCallLogs = btnCallLogs;
    this.btnConfiguration = btnConfiguration;
    this.btnSettings = btnSettings;
    this.cardCurrentCall = cardCurrentCall;
    this.statusIndicator = statusIndicator;
    this.switchForwarding = switchForwarding;
    this.toolbar = toolbar;
    this.tvCurrentCallNumber = tvCurrentCallNumber;
    this.tvCurrentCallStatus = tvCurrentCallStatus;
    this.tvForwardingStatus = tvForwardingStatus;
    this.tvStatusDetails = tvStatusDetails;
    this.tvTotalFailed = tvTotalFailed;
    this.tvTotalForwarded = tvTotalForwarded;
  }

  @Override
  @NonNull
  public CoordinatorLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnCallLogs;
      MaterialButton btnCallLogs = ViewBindings.findChildViewById(rootView, id);
      if (btnCallLogs == null) {
        break missingId;
      }

      id = R.id.btnConfiguration;
      MaterialButton btnConfiguration = ViewBindings.findChildViewById(rootView, id);
      if (btnConfiguration == null) {
        break missingId;
      }

      id = R.id.btnSettings;
      MaterialButton btnSettings = ViewBindings.findChildViewById(rootView, id);
      if (btnSettings == null) {
        break missingId;
      }

      id = R.id.cardCurrentCall;
      MaterialCardView cardCurrentCall = ViewBindings.findChildViewById(rootView, id);
      if (cardCurrentCall == null) {
        break missingId;
      }

      id = R.id.statusIndicator;
      View statusIndicator = ViewBindings.findChildViewById(rootView, id);
      if (statusIndicator == null) {
        break missingId;
      }

      id = R.id.switchForwarding;
      SwitchMaterial switchForwarding = ViewBindings.findChildViewById(rootView, id);
      if (switchForwarding == null) {
        break missingId;
      }

      id = R.id.toolbar;
      MaterialToolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.tvCurrentCallNumber;
      TextView tvCurrentCallNumber = ViewBindings.findChildViewById(rootView, id);
      if (tvCurrentCallNumber == null) {
        break missingId;
      }

      id = R.id.tvCurrentCallStatus;
      TextView tvCurrentCallStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvCurrentCallStatus == null) {
        break missingId;
      }

      id = R.id.tvForwardingStatus;
      TextView tvForwardingStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvForwardingStatus == null) {
        break missingId;
      }

      id = R.id.tvStatusDetails;
      TextView tvStatusDetails = ViewBindings.findChildViewById(rootView, id);
      if (tvStatusDetails == null) {
        break missingId;
      }

      id = R.id.tvTotalFailed;
      TextView tvTotalFailed = ViewBindings.findChildViewById(rootView, id);
      if (tvTotalFailed == null) {
        break missingId;
      }

      id = R.id.tvTotalForwarded;
      TextView tvTotalForwarded = ViewBindings.findChildViewById(rootView, id);
      if (tvTotalForwarded == null) {
        break missingId;
      }

      return new ActivityMainBinding((CoordinatorLayout) rootView, btnCallLogs, btnConfiguration,
          btnSettings, cardCurrentCall, statusIndicator, switchForwarding, toolbar,
          tvCurrentCallNumber, tvCurrentCallStatus, tvForwardingStatus, tvStatusDetails,
          tvTotalFailed, tvTotalForwarded);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
