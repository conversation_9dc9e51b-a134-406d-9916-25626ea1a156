-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:2:1-120:12
INJECTED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:2:1-120:12
INJECTED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:2:1-120:12
INJECTED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:2:1-120:12
MERGED from [androidx.databinding:databinding-adapters:8.1.2] /Users/<USER>/.gradle/caches/8.10/transforms/315b8622b523bc40779937f1c745c614/transformed/databinding-adapters-8.1.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-ktx:8.1.2] /Users/<USER>/.gradle/caches/8.10/transforms/781a7aa95e0a86688790770a407abbf1/transformed/jetified-databinding-ktx-8.1.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:databinding-runtime:8.1.2] /Users/<USER>/.gradle/caches/8.10/transforms/b23a16b7b0131a48fd7ffadf311ccc9b/transformed/databinding-runtime-8.1.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:8.1.2] /Users/<USER>/.gradle/caches/8.10/transforms/c16a4e4669677468baf7c8a24b52282d/transformed/jetified-viewbinding-8.1.2/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.10.0] /Users/<USER>/.gradle/caches/8.10/transforms/6acec092cc653e34676ce455eae5b61f/transformed/material-1.10.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/8.10/transforms/18635bf40db815413205a38e8a73f34f/transformed/constraintlayout-2.1.4/AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/a6c35266896f2ea0a05f7fdc28d34949/transformed/jetified-appcompat-resources-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/3580a7a86416f0c987c68a95cd38672e/transformed/appcompat-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/0c24fbcac79485ba2ba041aceec7fe06/transformed/jetified-viewpager2-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.6.2] /Users/<USER>/.gradle/caches/8.10/transforms/ec19ebc7e003b80add4308de17dd1c58/transformed/fragment-1.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.6.2] /Users/<USER>/.gradle/caches/8.10/transforms/0b2f5cfd2fa7c930f1c14f86229d1b82/transformed/jetified-fragment-ktx-1.6.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.8.2] /Users/<USER>/.gradle/caches/8.10/transforms/934d032ef5b3b32baa2f263d4a7385d5/transformed/jetified-activity-1.8.2/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.2] /Users/<USER>/.gradle/caches/8.10/transforms/b857dba085e192669fc59695bc457ea9/transformed/jetified-activity-ktx-1.8.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/2bf6ec78eb12d6e4d1c4fe660a5f72a6/transformed/jetified-emoji2-views-helper-1.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/a30e1fe6c4ed5ace2aadb0cb60127282/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/8221539abda50c7aedc05e473fd47ad2/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.10/transforms/1dc42bb9c5b0070ee72e9009dbaf4cd3/transformed/jetified-savedstate-ktx-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.10/transforms/63403f23b76b93551d7a499f12ad436f/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/e40ebc3ede25fe10436e2b9a67790948/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.work:work-runtime-ktx:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/6a345963804fd3e24cc26789278beadc/transformed/work-runtime-ktx-2.9.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:17:1-145:12
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/677e779bf35e6ed8651937a72b645a55/transformed/jetified-lifecycle-service-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/6bad45c3e73cba33f913594ccdebc03b/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/44c1752f14d0532a668a314e4ca69cae/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/f50db468e75a483a8e1aeaae39f1ddbf/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/0cf4e4dfa7a905aba102e878edada2b1/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/97e9b545d237febaa8a0dbb7e35b7bd3/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/e40b5ad95789bfc1e70901a054018305/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/239f3631f2f97280cf20bf62ea9a2d0a/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/b9b237c65d148162dba5f518ac7d9336/transformed/jetified-core-ktx-1.12.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/f471d96919ee831700aaa7793cb88db9/transformed/drawerlayout-1.1.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /Users/<USER>/.gradle/caches/8.10/transforms/a0b59593001da7f2cbb8314c1e949cdd/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/109750b43eadf1884097820777268d11/transformed/transition-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.10/transforms/b98e0dd69eca7289f5dbf5db90451d4d/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.10/transforms/1979e4472e7184cbcc9247a33cc295fc/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.10/transforms/44a1313f6a65366fd9f905171e2332a1/transformed/recyclerview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/ed2c2b1172657de303c6424b20d7db79/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.10/transforms/eef1455e8e30c23f2659bc96e37acf8b/transformed/customview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/2e37c0ed8bc86c66b0dc0e6a1f68ae10/transformed/core-1.12.0/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/f2895f8d4db9723d89c31371ae6a2cfd/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/c876107dc7f1e228a2885a84a350be88/transformed/jetified-lifecycle-livedata-ktx-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/e275caffd8e058d114a615caf06cf4d7/transformed/jetified-lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/799b3444209b39e0ee5a8c274cc0fb85/transformed/jetified-lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/8d1765fc745ef3335caadcdc97a01918/transformed/room-runtime-2.6.1/AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/8a67b237605bc78a58fd00d9839fea0f/transformed/jetified-room-ktx-2.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:2:1-31:12
MERGED from [com.jakewharton.timber:timber:5.0.1] /Users/<USER>/.gradle/caches/8.10/transforms/f18d110bcaa53f16f3c66f23273b0128/transformed/jetified-timber-5.0.1/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/8612de94980c58cfb9c197fb4da5d605/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/e76478bf70112012d4563170db171db4/transformed/cardview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/b256fdd9a0f4d0e56cb7b72d09215aa8/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/723894d40f040f2d99322a3b4d924481/transformed/sqlite-framework-2.4.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/42896e24c29f36fd51c05c60647a5de5/transformed/sqlite-2.4.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/05fdd7320b689b0f588cc353d67746f9/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/a525d8d8fcd3bc1d19680789526cfec5/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/f6271a2bea491e87ad8153945b8d7eb3/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/58224127ae9760f8e1f5fb405cd47539/transformed/jetified-tracing-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/6ddafb5c69de620b67d5eb591676119f/transformed/documentfile-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/ee0e7e1352621777a0973a40baf7c500/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/c4c056daa125f42ad9379b9139bf5ff4/transformed/print-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a80e1e66913b6222963e0fd0b7173b24/transformed/jetified-annotation-experimental-1.3.0/AndroidManifest.xml:17:1-22:12
	package
		INJECTED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml
	xmlns:tools
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:2:11-69
uses-permission#android.permission.READ_PHONE_STATE
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:6:5-75
	android:name
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:6:22-72
uses-permission#android.permission.READ_CALL_LOG
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:7:5-72
	android:name
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:7:22-69
uses-permission#android.permission.ANSWER_PHONE_CALLS
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:8:5-77
	android:name
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:8:22-74
uses-permission#android.permission.RECORD_AUDIO
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:9:5-71
MERGED from [org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:11:5-71
MERGED from [org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:11:5-71
	android:name
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:9:22-68
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:10:5-80
MERGED from [org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:25:5-80
MERGED from [org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:25:5-80
	android:name
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:10:22-77
uses-permission#android.permission.MANAGE_OWN_CALLS
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:11:5-75
	android:name
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:11:22-72
uses-permission#android.permission.CALL_PHONE
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:12:5-69
	android:name
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:12:22-66
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:15:5-67
MERGED from [org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:10:5-67
MERGED from [org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:10:5-67
	android:name
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:15:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:16:5-79
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:24:5-79
MERGED from [org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:12:5-79
MERGED from [org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:12:5-79
	android:name
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:16:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:17:5-76
	android:name
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:17:22-73
uses-permission#android.permission.CHANGE_WIFI_STATE
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:18:5-76
	android:name
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:18:22-73
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:21:5-77
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:26:5-77
MERGED from [org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:13:5-77
MERGED from [org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:13:5-77
	android:name
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:21:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_PHONE_CALL
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:22:5-88
	android:name
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:22:22-85
uses-permission#android.permission.WAKE_LOCK
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:23:5-68
MERGED from [org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:14:5-68
MERGED from [org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:14:5-68
	android:name
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:23:22-65
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:24:5-78
	android:name
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:24:22-75
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:27:5-81
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:25:5-81
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:25:5-81
	android:name
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:27:22-78
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:30:5-31:38
	android:maxSdkVersion
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:31:9-35
	android:name
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:30:22-78
uses-feature#android.hardware.telephony
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:34:5-36:35
	android:required
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:36:9-32
	android:name
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:35:9-50
uses-feature#android.hardware.microphone
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:37:5-39:35
	android:required
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:39:9-32
	android:name
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:38:9-51
application
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:41:5-118:19
INJECTED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:41:5-118:19
MERGED from [com.google.android.material:material:1.10.0] /Users/<USER>/.gradle/caches/8.10/transforms/6acec092cc653e34676ce455eae5b61f/transformed/material-1.10.0/AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.10.0] /Users/<USER>/.gradle/caches/8.10/transforms/6acec092cc653e34676ce455eae5b61f/transformed/material-1.10.0/AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/8.10/transforms/18635bf40db815413205a38e8a73f34f/transformed/constraintlayout-2.1.4/AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/8.10/transforms/18635bf40db815413205a38e8a73f34f/transformed/constraintlayout-2.1.4/AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/a30e1fe6c4ed5ace2aadb0cb60127282/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/a30e1fe6c4ed5ace2aadb0cb60127282/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:28:5-143:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/6bad45c3e73cba33f913594ccdebc03b/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/6bad45c3e73cba33f913594ccdebc03b/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/2e37c0ed8bc86c66b0dc0e6a1f68ae10/transformed/core-1.12.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/2e37c0ed8bc86c66b0dc0e6a1f68ae10/transformed/core-1.12.0/AndroidManifest.xml:28:5-89
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/8d1765fc745ef3335caadcdc97a01918/transformed/room-runtime-2.6.1/AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/8d1765fc745ef3335caadcdc97a01918/transformed/room-runtime-2.6.1/AndroidManifest.xml:23:5-29:19
MERGED from [com.jakewharton.timber:timber:5.0.1] /Users/<USER>/.gradle/caches/8.10/transforms/f18d110bcaa53f16f3c66f23273b0128/transformed/jetified-timber-5.0.1/AndroidManifest.xml:7:5-20
MERGED from [com.jakewharton.timber:timber:5.0.1] /Users/<USER>/.gradle/caches/8.10/transforms/f18d110bcaa53f16f3c66f23273b0128/transformed/jetified-timber-5.0.1/AndroidManifest.xml:7:5-20
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/a525d8d8fcd3bc1d19680789526cfec5/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/a525d8d8fcd3bc1d19680789526cfec5/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/f6271a2bea491e87ad8153945b8d7eb3/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/f6271a2bea491e87ad8153945b8d7eb3/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/2e37c0ed8bc86c66b0dc0e6a1f68ae10/transformed/core-1.12.0/AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:49:9-35
	android:label
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:47:9-41
	android:fullBackupContent
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:45:9-54
	android:roundIcon
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:48:9-54
	tools:targetApi
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:51:9-29
	android:icon
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:46:9-43
	android:allowBackup
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:43:9-35
	android:theme
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:50:9-55
	android:dataExtractionRules
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:44:9-65
	android:name
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:42:9-49
activity#com.voipforwarder.app.ui.MainActivity
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:54:9-63:20
	android:launchMode
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:57:13-43
	android:exported
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:56:13-36
	android:theme
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:58:13-59
	android:name
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:55:13-44
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:59:13-62:29
action#android.intent.action.MAIN
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:60:17-69
	android:name
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:60:25-66
category#android.intent.category.LAUNCHER
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:61:17-77
	android:name
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:61:27-74
service#com.voipforwarder.app.service.CallForwardingService
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:66:9-70:57
	android:enabled
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:68:13-35
	android:exported
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:69:13-37
	android:foregroundServiceType
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:70:13-54
	android:name
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:67:13-58
service#com.voipforwarder.app.service.VoIPService
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:73:9-77:57
	android:enabled
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:75:13-35
	android:exported
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:76:13-37
	android:foregroundServiceType
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:77:13-54
	android:name
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:74:13-48
service#com.voipforwarder.app.service.CallScreeningServiceImpl
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:80:9-87:19
	android:exported
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:83:13-36
	android:permission
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:82:13-75
	android:name
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:81:13-61
intent-filter#action:name:android.telecom.CallScreeningService
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:84:13-86:29
action#android.telecom.CallScreeningService
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:85:17-79
	android:name
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:85:25-76
receiver#com.voipforwarder.app.receiver.PhoneStateReceiver
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:90:9-97:20
	android:enabled
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:92:13-35
	android:exported
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:93:13-36
	android:name
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:91:13-56
intent-filter#action:name:android.intent.action.PHONE_STATE
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:94:13-96:29
	android:priority
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:94:28-51
action#android.intent.action.PHONE_STATE
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:95:17-76
	android:name
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:95:25-73
receiver#com.voipforwarder.app.receiver.BootReceiver
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:100:9-110:20
	android:enabled
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:102:13-35
	android:exported
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:103:13-36
	android:name
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:101:13-50
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MY_PACKAGE_REPLACED+action:name:android.intent.action.PACKAGE_REPLACED+data:scheme:package
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:104:13-109:29
	android:priority
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:104:28-51
action#android.intent.action.BOOT_COMPLETED
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:105:17-79
	android:name
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:105:25-76
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:106:17-84
	android:name
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:106:25-81
action#android.intent.action.PACKAGE_REPLACED
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:107:17-81
	android:name
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:107:25-78
data
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:108:17-50
	android:scheme
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:108:23-47
service#com.voipforwarder.app.service.CallLogObserverService
ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:113:9-116:40
	android:enabled
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:115:13-35
	android:exported
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:116:13-37
	android:name
		ADDED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:114:13-59
uses-sdk
INJECTED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml
INJECTED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml
MERGED from [androidx.databinding:databinding-adapters:8.1.2] /Users/<USER>/.gradle/caches/8.10/transforms/315b8622b523bc40779937f1c745c614/transformed/databinding-adapters-8.1.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-adapters:8.1.2] /Users/<USER>/.gradle/caches/8.10/transforms/315b8622b523bc40779937f1c745c614/transformed/databinding-adapters-8.1.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.1.2] /Users/<USER>/.gradle/caches/8.10/transforms/781a7aa95e0a86688790770a407abbf1/transformed/jetified-databinding-ktx-8.1.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-ktx:8.1.2] /Users/<USER>/.gradle/caches/8.10/transforms/781a7aa95e0a86688790770a407abbf1/transformed/jetified-databinding-ktx-8.1.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.1.2] /Users/<USER>/.gradle/caches/8.10/transforms/b23a16b7b0131a48fd7ffadf311ccc9b/transformed/databinding-runtime-8.1.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:databinding-runtime:8.1.2] /Users/<USER>/.gradle/caches/8.10/transforms/b23a16b7b0131a48fd7ffadf311ccc9b/transformed/databinding-runtime-8.1.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.1.2] /Users/<USER>/.gradle/caches/8.10/transforms/c16a4e4669677468baf7c8a24b52282d/transformed/jetified-viewbinding-8.1.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.1.2] /Users/<USER>/.gradle/caches/8.10/transforms/c16a4e4669677468baf7c8a24b52282d/transformed/jetified-viewbinding-8.1.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.10.0] /Users/<USER>/.gradle/caches/8.10/transforms/6acec092cc653e34676ce455eae5b61f/transformed/material-1.10.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.10.0] /Users/<USER>/.gradle/caches/8.10/transforms/6acec092cc653e34676ce455eae5b61f/transformed/material-1.10.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/8.10/transforms/18635bf40db815413205a38e8a73f34f/transformed/constraintlayout-2.1.4/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] /Users/<USER>/.gradle/caches/8.10/transforms/18635bf40db815413205a38e8a73f34f/transformed/constraintlayout-2.1.4/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/a6c35266896f2ea0a05f7fdc28d34949/transformed/jetified-appcompat-resources-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/a6c35266896f2ea0a05f7fdc28d34949/transformed/jetified-appcompat-resources-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/3580a7a86416f0c987c68a95cd38672e/transformed/appcompat-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/3580a7a86416f0c987c68a95cd38672e/transformed/appcompat-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/0c24fbcac79485ba2ba041aceec7fe06/transformed/jetified-viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/0c24fbcac79485ba2ba041aceec7fe06/transformed/jetified-viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.6.2] /Users/<USER>/.gradle/caches/8.10/transforms/ec19ebc7e003b80add4308de17dd1c58/transformed/fragment-1.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] /Users/<USER>/.gradle/caches/8.10/transforms/ec19ebc7e003b80add4308de17dd1c58/transformed/fragment-1.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] /Users/<USER>/.gradle/caches/8.10/transforms/0b2f5cfd2fa7c930f1c14f86229d1b82/transformed/jetified-fragment-ktx-1.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.2] /Users/<USER>/.gradle/caches/8.10/transforms/0b2f5cfd2fa7c930f1c14f86229d1b82/transformed/jetified-fragment-ktx-1.6.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] /Users/<USER>/.gradle/caches/8.10/transforms/934d032ef5b3b32baa2f263d4a7385d5/transformed/jetified-activity-1.8.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] /Users/<USER>/.gradle/caches/8.10/transforms/934d032ef5b3b32baa2f263d4a7385d5/transformed/jetified-activity-1.8.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] /Users/<USER>/.gradle/caches/8.10/transforms/b857dba085e192669fc59695bc457ea9/transformed/jetified-activity-ktx-1.8.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] /Users/<USER>/.gradle/caches/8.10/transforms/b857dba085e192669fc59695bc457ea9/transformed/jetified-activity-ktx-1.8.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/2bf6ec78eb12d6e4d1c4fe660a5f72a6/transformed/jetified-emoji2-views-helper-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/2bf6ec78eb12d6e4d1c4fe660a5f72a6/transformed/jetified-emoji2-views-helper-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/a30e1fe6c4ed5ace2aadb0cb60127282/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/a30e1fe6c4ed5ace2aadb0cb60127282/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/8221539abda50c7aedc05e473fd47ad2/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/8221539abda50c7aedc05e473fd47ad2/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.10/transforms/1dc42bb9c5b0070ee72e9009dbaf4cd3/transformed/jetified-savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.10/transforms/1dc42bb9c5b0070ee72e9009dbaf4cd3/transformed/jetified-savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.10/transforms/63403f23b76b93551d7a499f12ad436f/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.10/transforms/63403f23b76b93551d7a499f12ad436f/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/e40ebc3ede25fe10436e2b9a67790948/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/e40ebc3ede25fe10436e2b9a67790948/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.work:work-runtime-ktx:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/6a345963804fd3e24cc26789278beadc/transformed/work-runtime-ktx-2.9.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/6a345963804fd3e24cc26789278beadc/transformed/work-runtime-ktx-2.9.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/677e779bf35e6ed8651937a72b645a55/transformed/jetified-lifecycle-service-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/677e779bf35e6ed8651937a72b645a55/transformed/jetified-lifecycle-service-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/6bad45c3e73cba33f913594ccdebc03b/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/6bad45c3e73cba33f913594ccdebc03b/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/44c1752f14d0532a668a314e4ca69cae/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/44c1752f14d0532a668a314e4ca69cae/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/f50db468e75a483a8e1aeaae39f1ddbf/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/f50db468e75a483a8e1aeaae39f1ddbf/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/0cf4e4dfa7a905aba102e878edada2b1/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/0cf4e4dfa7a905aba102e878edada2b1/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/97e9b545d237febaa8a0dbb7e35b7bd3/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/97e9b545d237febaa8a0dbb7e35b7bd3/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/e40b5ad95789bfc1e70901a054018305/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/e40b5ad95789bfc1e70901a054018305/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/239f3631f2f97280cf20bf62ea9a2d0a/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/239f3631f2f97280cf20bf62ea9a2d0a/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/b9b237c65d148162dba5f518ac7d9336/transformed/jetified-core-ktx-1.12.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/b9b237c65d148162dba5f518ac7d9336/transformed/jetified-core-ktx-1.12.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/f471d96919ee831700aaa7793cb88db9/transformed/drawerlayout-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/f471d96919ee831700aaa7793cb88db9/transformed/drawerlayout-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /Users/<USER>/.gradle/caches/8.10/transforms/a0b59593001da7f2cbb8314c1e949cdd/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /Users/<USER>/.gradle/caches/8.10/transforms/a0b59593001da7f2cbb8314c1e949cdd/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/109750b43eadf1884097820777268d11/transformed/transition-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/109750b43eadf1884097820777268d11/transformed/transition-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.10/transforms/b98e0dd69eca7289f5dbf5db90451d4d/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.10/transforms/b98e0dd69eca7289f5dbf5db90451d4d/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.10/transforms/1979e4472e7184cbcc9247a33cc295fc/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.10/transforms/1979e4472e7184cbcc9247a33cc295fc/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.10/transforms/44a1313f6a65366fd9f905171e2332a1/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.10/transforms/44a1313f6a65366fd9f905171e2332a1/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/ed2c2b1172657de303c6424b20d7db79/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/ed2c2b1172657de303c6424b20d7db79/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.10/transforms/eef1455e8e30c23f2659bc96e37acf8b/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.10/transforms/eef1455e8e30c23f2659bc96e37acf8b/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/2e37c0ed8bc86c66b0dc0e6a1f68ae10/transformed/core-1.12.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/2e37c0ed8bc86c66b0dc0e6a1f68ae10/transformed/core-1.12.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/f2895f8d4db9723d89c31371ae6a2cfd/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/f2895f8d4db9723d89c31371ae6a2cfd/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/c876107dc7f1e228a2885a84a350be88/transformed/jetified-lifecycle-livedata-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/c876107dc7f1e228a2885a84a350be88/transformed/jetified-lifecycle-livedata-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/e275caffd8e058d114a615caf06cf4d7/transformed/jetified-lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/e275caffd8e058d114a615caf06cf4d7/transformed/jetified-lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/799b3444209b39e0ee5a8c274cc0fb85/transformed/jetified-lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/799b3444209b39e0ee5a8c274cc0fb85/transformed/jetified-lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/8d1765fc745ef3335caadcdc97a01918/transformed/room-runtime-2.6.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/8d1765fc745ef3335caadcdc97a01918/transformed/room-runtime-2.6.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/8a67b237605bc78a58fd00d9839fea0f/transformed/jetified-room-ktx-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/8a67b237605bc78a58fd00d9839fea0f/transformed/jetified-room-ktx-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:5:5-7:41
MERGED from [org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:5:5-7:41
MERGED from [com.jakewharton.timber:timber:5.0.1] /Users/<USER>/.gradle/caches/8.10/transforms/f18d110bcaa53f16f3c66f23273b0128/transformed/jetified-timber-5.0.1/AndroidManifest.xml:5:5-44
MERGED from [com.jakewharton.timber:timber:5.0.1] /Users/<USER>/.gradle/caches/8.10/transforms/f18d110bcaa53f16f3c66f23273b0128/transformed/jetified-timber-5.0.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/8612de94980c58cfb9c197fb4da5d605/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/8612de94980c58cfb9c197fb4da5d605/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/e76478bf70112012d4563170db171db4/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/e76478bf70112012d4563170db171db4/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/b256fdd9a0f4d0e56cb7b72d09215aa8/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/b256fdd9a0f4d0e56cb7b72d09215aa8/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/723894d40f040f2d99322a3b4d924481/transformed/sqlite-framework-2.4.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/723894d40f040f2d99322a3b4d924481/transformed/sqlite-framework-2.4.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/42896e24c29f36fd51c05c60647a5de5/transformed/sqlite-2.4.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] /Users/<USER>/.gradle/caches/8.10/transforms/42896e24c29f36fd51c05c60647a5de5/transformed/sqlite-2.4.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/05fdd7320b689b0f588cc353d67746f9/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/05fdd7320b689b0f588cc353d67746f9/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/a525d8d8fcd3bc1d19680789526cfec5/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/a525d8d8fcd3bc1d19680789526cfec5/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/f6271a2bea491e87ad8153945b8d7eb3/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/f6271a2bea491e87ad8153945b8d7eb3/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/58224127ae9760f8e1f5fb405cd47539/transformed/jetified-tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/58224127ae9760f8e1f5fb405cd47539/transformed/jetified-tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/6ddafb5c69de620b67d5eb591676119f/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/6ddafb5c69de620b67d5eb591676119f/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/ee0e7e1352621777a0973a40baf7c500/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/ee0e7e1352621777a0973a40baf7c500/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/c4c056daa125f42ad9379b9139bf5ff4/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.10/transforms/c4c056daa125f42ad9379b9139bf5ff4/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a80e1e66913b6222963e0fd0b7173b24/transformed/jetified-annotation-experimental-1.3.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/a80e1e66913b6222963e0fd0b7173b24/transformed/jetified-annotation-experimental-1.3.0/AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/a30e1fe6c4ed5ace2aadb0cb60127282/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:29:9-37:20
MERGED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:29:9-37:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/6bad45c3e73cba33f913594ccdebc03b/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/6bad45c3e73cba33f913594ccdebc03b/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/f6271a2bea491e87ad8153945b8d7eb3/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.10/transforms/f6271a2bea491e87ad8153945b8d7eb3/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/a30e1fe6c4ed5ace2aadb0cb60127282/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/a30e1fe6c4ed5ace2aadb0cb60127282/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/a30e1fe6c4ed5ace2aadb0cb60127282/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/a30e1fe6c4ed5ace2aadb0cb60127282/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/a30e1fe6c4ed5ace2aadb0cb60127282/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/a30e1fe6c4ed5ace2aadb0cb60127282/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/a30e1fe6c4ed5ace2aadb0cb60127282/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:30:17-75
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:116:13-120:29
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:140:25-85
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/6bad45c3e73cba33f913594ccdebc03b/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/6bad45c3e73cba33f913594ccdebc03b/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/6bad45c3e73cba33f913594ccdebc03b/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/2e37c0ed8bc86c66b0dc0e6a1f68ae10/transformed/core-1.12.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/2e37c0ed8bc86c66b0dc0e6a1f68ae10/transformed/core-1.12.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/2e37c0ed8bc86c66b0dc0e6a1f68ae10/transformed/core-1.12.0/AndroidManifest.xml:23:9-81
permission#com.voipforwarder.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/2e37c0ed8bc86c66b0dc0e6a1f68ae10/transformed/core-1.12.0/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/2e37c0ed8bc86c66b0dc0e6a1f68ae10/transformed/core-1.12.0/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/2e37c0ed8bc86c66b0dc0e6a1f68ae10/transformed/core-1.12.0/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/2e37c0ed8bc86c66b0dc0e6a1f68ae10/transformed/core-1.12.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/2e37c0ed8bc86c66b0dc0e6a1f68ae10/transformed/core-1.12.0/AndroidManifest.xml:26:22-94
uses-permission#com.voipforwarder.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/2e37c0ed8bc86c66b0dc0e6a1f68ae10/transformed/core-1.12.0/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/2e37c0ed8bc86c66b0dc0e6a1f68ae10/transformed/core-1.12.0/AndroidManifest.xml:26:22-94
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/8d1765fc745ef3335caadcdc97a01918/transformed/room-runtime-2.6.1/AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/8d1765fc745ef3335caadcdc97a01918/transformed/room-runtime-2.6.1/AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/8d1765fc745ef3335caadcdc97a01918/transformed/room-runtime-2.6.1/AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/8d1765fc745ef3335caadcdc97a01918/transformed/room-runtime-2.6.1/AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/8d1765fc745ef3335caadcdc97a01918/transformed/room-runtime-2.6.1/AndroidManifest.xml:25:13-74
uses-permission#android.permission.CHANGE_WIFI_MULTICAST_STATE
ADDED from [org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:16:5-86
	android:name
		ADDED from [org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:16:22-83
uses-permission#android.permission.CAMERA
ADDED from [org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:17:5-65
	android:name
		ADDED from [org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:17:22-62
uses-feature#android.hardware.camera
ADDED from [org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:19:5-21:36
	android:required
		ADDED from [org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:21:9-33
	android:name
		ADDED from [org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:20:9-47
uses-permission#android.permission.BLUETOOTH
ADDED from [org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:23:5-68
	android:name
		ADDED from [org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:23:22-65
uses-permission#android.permission.VIBRATE
ADDED from [org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:27:5-66
	android:name
		ADDED from [org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:27:22-63
uses-permission#android.permission.ACCESS_NOTIFICATION_POLICY
ADDED from [org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:29:5-85
	android:name
		ADDED from [org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:29:22-82
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:50:25-92
