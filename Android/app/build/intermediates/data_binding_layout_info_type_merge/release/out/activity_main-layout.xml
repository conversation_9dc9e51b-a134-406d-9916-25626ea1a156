<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.voipforwarder.app" filePath="app/src/main/res/layout/activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="261" endOffset="53"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="12" startOffset="8" endLine="16" endOffset="42"/></Target><Target id="@+id/statusIndicator" view="View"><Expressions/><location startLine="57" startOffset="24" endLine="59" endOffset="60"/></Target><Target id="@+id/tvForwardingStatus" view="TextView"><Expressions/><location startLine="61" startOffset="24" endLine="67" endOffset="80"/></Target><Target id="@+id/switchForwarding" view="com.google.android.material.switchmaterial.SwitchMaterial"><Expressions/><location startLine="69" startOffset="24" endLine="72" endOffset="66"/></Target><Target id="@+id/tvStatusDetails" view="TextView"><Expressions/><location startLine="76" startOffset="20" endLine="83" endOffset="78"/></Target><Target id="@+id/cardCurrentCall" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="90" startOffset="12" endLine="129" endOffset="63"/></Target><Target id="@+id/tvCurrentCallNumber" view="TextView"><Expressions/><location startLine="110" startOffset="20" endLine="116" endOffset="50"/></Target><Target id="@+id/tvCurrentCallStatus" view="TextView"><Expressions/><location startLine="118" startOffset="20" endLine="125" endOffset="67"/></Target><Target id="@+id/btnConfiguration" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="150" startOffset="20" endLine="156" endOffset="58"/></Target><Target id="@+id/btnCallLogs" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="158" startOffset="20" endLine="164" endOffset="57"/></Target><Target id="@+id/btnSettings" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="166" startOffset="20" endLine="172" endOffset="62"/></Target><Target id="@+id/tvTotalForwarded" view="TextView"><Expressions/><location startLine="210" startOffset="28" endLine="216" endOffset="58"/></Target><Target id="@+id/tvTotalFailed" view="TextView"><Expressions/><location startLine="234" startOffset="28" endLine="240" endOffset="58"/></Target></Targets></Layout>