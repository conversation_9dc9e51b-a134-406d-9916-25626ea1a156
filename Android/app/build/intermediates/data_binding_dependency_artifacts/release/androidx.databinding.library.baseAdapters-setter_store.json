{"version": 5, "adapterMethods": {"android:animateLayoutChanges": [[{"viewType": "android.view.ViewGroup", "valueType": "boolean"}, {"type": "androidx.databinding.adapters.ViewGroupBindingAdapter", "method": "setAnimateLayoutChanges", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:autoText": [[{"viewType": "android.widget.TextView", "valueType": "boolean"}, {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setAutoText", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:background": [[{"viewType": "android.view.View", "valueType": "android.graphics.drawable.Drawable"}, {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setBackground", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:bufferType": [[{"viewType": "android.widget.TextView", "valueType": "android.widget.TextView.BufferType"}, {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setBufferType", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:capitalize": [[{"viewType": "android.widget.TextView", "valueType": "android.text.method.TextKeyListener.Capitalize"}, {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setCapitalize", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:checked": [[{"viewType": "android.widget.CompoundButton", "valueType": "boolean"}, {"type": "androidx.databinding.adapters.CompoundButtonBindingAdapter", "method": "setChecked", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:checkedButton": [[{"viewType": "android.widget.RadioGroup", "valueType": "int"}, {"type": "androidx.databinding.adapters.RadioGroupBindingAdapter", "method": "setCheckedButton", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:collapseColumns": [[{"viewType": "android.widget.TableLayout", "valueType": "java.lang.CharSequence"}, {"type": "androidx.databinding.adapters.TableLayoutBindingAdapter", "method": "setCollapseColumns", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:currentTab": [[{"viewType": "android.widget.TabHost", "valueType": "int"}, {"type": "androidx.databinding.adapters.TabHostBindingAdapter", "method": "setCurrentTab", "requiresOldValue": false, "isStatic": true, "componentClass": null}], [{"viewType": "android.widget.TabHost", "valueType": "java.lang.String"}, {"type": "androidx.databinding.adapters.TabHostBindingAdapter", "method": "setCurrentTabTag", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:date": [[{"viewType": "android.widget.CalendarView", "valueType": "long"}, {"type": "androidx.databinding.adapters.CalendarViewBindingAdapter", "method": "setDate", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:digits": [[{"viewType": "android.widget.TextView", "valueType": "java.lang.CharSequence"}, {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setDigits", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:drawableBottom": [[{"viewType": "android.widget.TextView", "valueType": "android.graphics.drawable.Drawable"}, {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setDrawableBottom", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:drawableEnd": [[{"viewType": "android.widget.TextView", "valueType": "android.graphics.drawable.Drawable"}, {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setDrawableEnd", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:drawableLeft": [[{"viewType": "android.widget.TextView", "valueType": "android.graphics.drawable.Drawable"}, {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setDrawableLeft", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:drawableRight": [[{"viewType": "android.widget.TextView", "valueType": "android.graphics.drawable.Drawable"}, {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setDrawableRight", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:drawableStart": [[{"viewType": "android.widget.TextView", "valueType": "android.graphics.drawable.Drawable"}, {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setDrawableStart", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:drawableTop": [[{"viewType": "android.widget.TextView", "valueType": "android.graphics.drawable.Drawable"}, {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setDrawableTop", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:entries": [[{"viewType": "android.widget.AbsSpinner", "valueType": "java.lang.CharSequence[]"}, {"type": "androidx.databinding.adapters.AbsSpinnerBindingAdapter", "method": "setEntries", "requiresOldValue": false, "isStatic": true, "componentClass": null}], [{"viewType": "android.widget.AbsSpinner", "valueType": "java.util.List"}, {"type": "androidx.databinding.adapters.AbsSpinnerBindingAdapter", "method": "setEntries", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:hour": [[{"viewType": "android.widget.TimePicker", "valueType": "int"}, {"type": "androidx.databinding.adapters.TimePickerBindingAdapter", "method": "setHour", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:imeActionId": [[{"viewType": "android.widget.TextView", "valueType": "int"}, {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setImeActionLabel", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:imeActionLabel": [[{"viewType": "android.widget.TextView", "valueType": "java.lang.CharSequence"}, {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setImeActionLabel", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:inputMethod": [[{"viewType": "android.widget.TextView", "valueType": "java.lang.CharSequence"}, {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setInputMethod", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:lineSpacingExtra": [[{"viewType": "android.widget.TextView", "valueType": "float"}, {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setLineSpacingExtra", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:lineSpacingMultiplier": [[{"viewType": "android.widget.TextView", "valueType": "float"}, {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setLineSpacingMultiplier", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:maxLength": [[{"viewType": "android.widget.TextView", "valueType": "int"}, {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setMaxLength", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:minute": [[{"viewType": "android.widget.TimePicker", "valueType": "int"}, {"type": "androidx.databinding.adapters.TimePickerBindingAdapter", "method": "setMinute", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:numeric": [[{"viewType": "android.widget.TextView", "valueType": "int"}, {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setNumeric", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:onInflate": [[{"viewType": "androidx.databinding.ViewStubProxy", "valueType": "android.view.ViewStub.OnInflateListener"}, {"type": "androidx.databinding.adapters.ViewStubBindingAdapter", "method": "setOnInflateListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:onLayoutChange": [[{"viewType": "android.view.View", "valueType": "android.view.View.OnLayoutChangeListener"}, {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setOnLayoutChangeListener", "requiresOldValue": true, "isStatic": true, "componentClass": null}]], "android:padding": [[{"viewType": "android.view.View", "valueType": "float"}, {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setPadding", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:paddingBottom": [[{"viewType": "android.view.View", "valueType": "float"}, {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setPaddingBottom", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:paddingEnd": [[{"viewType": "android.view.View", "valueType": "float"}, {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setPaddingEnd", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:paddingLeft": [[{"viewType": "android.view.View", "valueType": "float"}, {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setPaddingLeft", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:paddingRight": [[{"viewType": "android.view.View", "valueType": "float"}, {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setPaddingRight", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:paddingStart": [[{"viewType": "android.view.View", "valueType": "float"}, {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setPaddingStart", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:paddingTop": [[{"viewType": "android.view.View", "valueType": "float"}, {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setPaddingTop", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:password": [[{"viewType": "android.widget.TextView", "valueType": "boolean"}, {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setPassword", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:phoneNumber": [[{"viewType": "android.widget.TextView", "valueType": "boolean"}, {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setPhoneNumber", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:progress": [[{"viewType": "android.widget.SeekBar", "valueType": "int"}, {"type": "androidx.databinding.adapters.SeekBarBindingAdapter", "method": "setProgress", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:rating": [[{"viewType": "android.widget.RatingBar", "valueType": "float"}, {"type": "androidx.databinding.adapters.RatingBarBindingAdapter", "method": "setRating", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:requiresFadingEdge": [[{"viewType": "android.view.View", "valueType": "int"}, {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setRequiresFadingEdge", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:selectedItemPosition": [[{"viewType": "android.widget.AdapterView", "valueType": "int"}, {"type": "androidx.databinding.adapters.AdapterViewBindingAdapter", "method": "setSelectedItemPosition", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:selection": [[{"viewType": "android.widget.AdapterView", "valueType": "int"}, {"type": "androidx.databinding.adapters.AdapterViewBindingAdapter", "method": "setSelection", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:shadowColor": [[{"viewType": "android.widget.TextView", "valueType": "int"}, {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setShadowColor", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:shadowDx": [[{"viewType": "android.widget.TextView", "valueType": "float"}, {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setShadowDx", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:shadowDy": [[{"viewType": "android.widget.TextView", "valueType": "float"}, {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setShadowDy", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:shadowRadius": [[{"viewType": "android.widget.TextView", "valueType": "float"}, {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setShadowRadius", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:shrinkColumns": [[{"viewType": "android.widget.TableLayout", "valueType": "java.lang.CharSequence"}, {"type": "androidx.databinding.adapters.TableLayoutBindingAdapter", "method": "setShrinkColumns", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:src": [[{"viewType": "android.widget.ImageView", "valueType": "android.graphics.drawable.Drawable"}, {"type": "androidx.databinding.adapters.ImageViewBindingAdapter", "method": "setImageDrawable", "requiresOldValue": false, "isStatic": true, "componentClass": null}], [{"viewType": "android.widget.ImageView", "valueType": "android.net.Uri"}, {"type": "androidx.databinding.adapters.ImageViewBindingAdapter", "method": "setImageUri", "requiresOldValue": false, "isStatic": true, "componentClass": null}], [{"viewType": "android.widget.ImageView", "valueType": "java.lang.String"}, {"type": "androidx.databinding.adapters.ImageViewBindingAdapter", "method": "setImageUri", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:stretchColumns": [[{"viewType": "android.widget.TableLayout", "valueType": "java.lang.CharSequence"}, {"type": "androidx.databinding.adapters.TableLayoutBindingAdapter", "method": "setStretchColumns", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:switchTextAppearance": [[{"viewType": "android.widget.Switch", "valueType": "int"}, {"type": "androidx.databinding.adapters.SwitchBindingAdapter", "method": "setSwitchTextAppearance", "requiresOldValue": false, "isStatic": true, "componentClass": null}], [{"viewType": "androidx.appcompat.widget.SwitchCompat", "valueType": "int"}, {"type": "androidx.databinding.adapters.SwitchCompatBindingAdapter", "method": "setSwitchTextAppearance", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:text": [[{"viewType": "android.widget.TextView", "valueType": "java.lang.CharSequence"}, {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setText", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:textSize": [[{"viewType": "android.widget.TextView", "valueType": "float"}, {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setTextSize", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:value": [[{"viewType": "android.widget.NumberPicker", "valueType": "int"}, {"type": "androidx.databinding.adapters.NumberPickerBindingAdapter", "method": "setValue", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "contentPadding": [[{"viewType": "androidx.cardview.widget.CardView", "valueType": "int"}, {"type": "androidx.databinding.adapters.CardViewBindingAdapter", "method": "setContentPadding", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "contentPaddingBottom": [[{"viewType": "androidx.cardview.widget.CardView", "valueType": "int"}, {"type": "androidx.databinding.adapters.CardViewBindingAdapter", "method": "setContentPaddingBottom", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "contentPaddingLeft": [[{"viewType": "androidx.cardview.widget.CardView", "valueType": "int"}, {"type": "androidx.databinding.adapters.CardViewBindingAdapter", "method": "setContentPaddingLeft", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "contentPaddingRight": [[{"viewType": "androidx.cardview.widget.CardView", "valueType": "int"}, {"type": "androidx.databinding.adapters.CardViewBindingAdapter", "method": "setContentPaddingRight", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "contentPaddingTop": [[{"viewType": "androidx.cardview.widget.CardView", "valueType": "int"}, {"type": "androidx.databinding.adapters.CardViewBindingAdapter", "method": "setContentPaddingTop", "requiresOldValue": false, "isStatic": true, "componentClass": null}]]}, "renamedMethods": {"android:alwaysDrawnWithCache": {"android.view.ViewGroup": {"type": "androidx.databinding.adapters.ViewGroupBindingAdapter", "method": "setAlwaysDrawnWithCacheEnabled", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:animationCache": {"android.view.ViewGroup": {"type": "androidx.databinding.adapters.ViewGroupBindingAdapter", "method": "setAnimationCacheEnabled", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:autoLink": {"android.widget.TextView": {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setAutoLinkMask", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:backgroundTint": {"android.view.View": {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setBackgroundTintList", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:buttonTint": {"android.widget.CompoundButton": {"type": "androidx.databinding.adapters.CompoundButtonBindingAdapter", "method": "setButtonTintList", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:checkMark": {"android.widget.CheckedTextView": {"type": "androidx.databinding.adapters.CheckedTextViewBindingAdapter", "method": "setCheckMarkDrawable", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:checkMarkTint": {"android.widget.CheckedTextView": {"type": "androidx.databinding.adapters.CheckedTextViewBindingAdapter", "method": "setCheckMarkTintList", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:completionThreshold": {"android.widget.AutoCompleteTextView": {"type": "androidx.databinding.adapters.AutoCompleteTextViewBindingAdapter", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:divider": {"android.widget.LinearLayout": {"type": "androidx.databinding.adapters.LinearLayoutBindingAdapter", "method": "setDividerDrawable", "requiresOldValue": false, "isStatic": true, "componentClass": null}, "android.widget.TabWidget": {"type": "androidx.databinding.adapters.TabWidgetBindingAdapter", "method": "setDividerDrawable", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:drawablePadding": {"android.widget.TextView": {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setCompoundDrawablePadding", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:editorExtras": {"android.widget.TextView": {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setInputExtras", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:fadeScrollbars": {"android.view.View": {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setScrollbarFadingEnabled", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:foregroundTint": {"android.widget.FrameLayout": {"type": "androidx.databinding.adapters.FrameLayoutBindingAdapter", "method": "setForegroundTintList", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:format": {"android.widget.NumberPicker": {"type": "androidx.databinding.adapters.NumberPickerBindingAdapter", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:getOutline": {"android.view.View": {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setOutlineProvider", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:indeterminateTint": {"android.widget.ProgressBar": {"type": "androidx.databinding.adapters.ProgressBarBindingAdapter", "method": "setIndeterminateTintList", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:inputType": {"android.widget.TextView": {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setRawInputType", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:layout": {"android.view.ViewStub": {"type": "androidx.databinding.adapters.ViewStubBindingAdapter", "method": "setLayoutResource", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:listSelector": {"android.widget.AbsListView": {"type": "androidx.databinding.adapters.AbsListViewBindingAdapter", "method": "setSelector", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:measureWithLargestChild": {"android.widget.LinearLayout": {"type": "androidx.databinding.adapters.LinearLayoutBindingAdapter", "method": "setMeasureWithLargestChildEnabled", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:nextFocusDown": {"android.view.View": {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setNextFocusDownId", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:nextFocusForward": {"android.view.View": {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setNextFocusForwardId", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:nextFocusLeft": {"android.view.View": {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setNextFocusLeftId", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:nextFocusRight": {"android.view.View": {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setNextFocusRightId", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:nextFocusUp": {"android.view.View": {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setNextFocusUpId", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:onApplyWindowInsets": {"android.view.View": {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setOnApplyWindowInsetsListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:onCheckedChanged": {"android.widget.CompoundButton": {"type": "androidx.databinding.adapters.CompoundButtonBindingAdapter", "method": "setOnCheckedChangeListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:onChildClick": {"android.widget.ExpandableListView": {"type": "androidx.databinding.adapters.ExpandableListViewBindingAdapter", "method": "setOnChildClickListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:onChronometerTick": {"android.widget.Chronometer": {"type": "androidx.databinding.adapters.ChronometerBindingAdapter", "method": "setOnChronometerTickListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:onClick": {"android.view.View": {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setOnClickListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:onClose": {"android.widget.SearchView": {"type": "androidx.databinding.adapters.SearchViewBindingAdapter", "method": "setOnCloseListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:onCompletion": {"android.widget.VideoView": {"type": "androidx.databinding.adapters.VideoViewBindingAdapter", "method": "setOnCompletionListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:onCreateContextMenu": {"android.view.View": {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setOnCreateContextMenuListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:onDismiss": {"android.widget.AutoCompleteTextView": {"type": "androidx.databinding.adapters.AutoCompleteTextViewBindingAdapter", "method": "setOnDismissListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:onDrag": {"android.view.View": {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setOnDragListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:onEditorAction": {"android.widget.TextView": {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setOnEditorActionListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:onError": {"android.widget.VideoView": {"type": "androidx.databinding.adapters.VideoViewBindingAdapter", "method": "setOnErrorListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:onFocusChange": {"android.view.View": {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setOnFocusChangeListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:onGenericMotion": {"android.view.View": {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setOnGenericMotionListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:onGroupClick": {"android.widget.ExpandableListView": {"type": "androidx.databinding.adapters.ExpandableListViewBindingAdapter", "method": "setOnGroupClickListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:onGroupCollapse": {"android.widget.ExpandableListView": {"type": "androidx.databinding.adapters.ExpandableListViewBindingAdapter", "method": "setOnGroupCollapseListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:onGroupExpand": {"android.widget.ExpandableListView": {"type": "androidx.databinding.adapters.ExpandableListViewBindingAdapter", "method": "setOnGroupExpandListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:onHover": {"android.view.View": {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setOnHoverListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:onInfo": {"android.widget.VideoView": {"type": "androidx.databinding.adapters.VideoViewBindingAdapter", "method": "setOnInfoListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:onItemClick": {"android.widget.AdapterView": {"type": "androidx.databinding.adapters.AdapterViewBindingAdapter", "method": "setOnItemClickListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}, "android.widget.AutoCompleteTextView": {"type": "androidx.databinding.adapters.AutoCompleteTextViewBindingAdapter", "method": "setOnItemClickListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:onItemLongClick": {"android.widget.AdapterView": {"type": "androidx.databinding.adapters.AdapterViewBindingAdapter", "method": "setOnItemLongClickListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:onKey": {"android.view.View": {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setOnKeyListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:onLongClick": {"android.view.View": {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setOnLongClickListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:onMenuItemClick": {"android.widget.ActionMenuView": {"type": "androidx.databinding.adapters.ActionMenuViewBindingAdapter", "method": "setOnMenuItemClickListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}, "android.widget.Toolbar": {"type": "androidx.databinding.adapters.ToolbarBindingAdapter", "method": "setOnMenuItemClickListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:onMovedToScrapHeap": {"android.widget.AbsListView": {"type": "androidx.databinding.adapters.AbsListViewBindingAdapter", "method": "setRecyclerListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:onNavigationClick": {"android.widget.Toolbar": {"type": "androidx.databinding.adapters.ToolbarBindingAdapter", "method": "setNavigationOnClickListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:onPrepared": {"android.widget.VideoView": {"type": "androidx.databinding.adapters.VideoViewBindingAdapter", "method": "setOnPreparedListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:onQueryTextFocusChange": {"android.widget.SearchView": {"type": "androidx.databinding.adapters.SearchViewBindingAdapter", "method": "setOnQueryTextFocusChangeListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:onScrollStateChange": {"android.widget.NumberPicker": {"type": "androidx.databinding.adapters.NumberPickerBindingAdapter", "method": "setOnScrollListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:onSearchClick": {"android.widget.SearchView": {"type": "androidx.databinding.adapters.SearchViewBindingAdapter", "method": "setOnSearchClickListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:onSystemUiVisibilityChange": {"android.view.View": {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setOnSystemUiVisibilityChangeListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:onTouch": {"android.view.View": {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setOnTouchListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:onZoomIn": {"android.widget.ZoomControls": {"type": "androidx.databinding.adapters.ZoomControlsBindingAdapter", "method": "setOnZoomInClickListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:onZoomOut": {"android.widget.ZoomControls": {"type": "androidx.databinding.adapters.ZoomControlsBindingAdapter", "method": "setOnZoomOutClickListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:popupBackground": {"android.widget.AutoCompleteTextView": {"type": "androidx.databinding.adapters.AutoCompleteTextViewBindingAdapter", "method": "setDropDownBackgroundDrawable", "requiresOldValue": false, "isStatic": true, "componentClass": null}, "android.widget.Spinner": {"type": "androidx.databinding.adapters.SpinnerBindingAdapter", "method": "setPopupBackgroundDrawable", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:progressTint": {"android.widget.ProgressBar": {"type": "androidx.databinding.adapters.ProgressBarBindingAdapter", "method": "setProgressTintList", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:requiresFadingEdge": {"android.view.View": {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setVerticalFadingEdgeEnabled", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:scrollHorizontally": {"android.widget.TextView": {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setHorizontallyScrolling", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:scrollbarDefaultDelayBeforeFade": {"android.view.View": {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setScrollBarDefaultDelayBeforeFade", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:scrollbarFadeDuration": {"android.view.View": {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setScrollBarFadeDuration", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:scrollbarSize": {"android.view.View": {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setScrollBarSize", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:scrollbarStyle": {"android.view.View": {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setScrollBarStyle", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:scrollingCache": {"android.widget.AbsListView": {"type": "androidx.databinding.adapters.AbsListViewBindingAdapter", "method": "setScrollingCacheEnabled", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:secondaryProgressTint": {"android.widget.ProgressBar": {"type": "androidx.databinding.adapters.ProgressBarBindingAdapter", "method": "setSecondaryProgressTintList", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:smoothScrollbar": {"android.widget.AbsListView": {"type": "androidx.databinding.adapters.AbsListViewBindingAdapter", "method": "setSmoothScrollbarEnabled", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:splitMotionEvents": {"android.view.ViewGroup": {"type": "androidx.databinding.adapters.ViewGroupBindingAdapter", "method": "setMotionEventSplittingEnabled", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:tabStripEnabled": {"android.widget.TabWidget": {"type": "androidx.databinding.adapters.TabWidgetBindingAdapter", "method": "setStripEnabled", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:tabStripLeft": {"android.widget.TabWidget": {"type": "androidx.databinding.adapters.TabWidgetBindingAdapter", "method": "setLeftStripDrawable", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:tabStripRight": {"android.widget.TabWidget": {"type": "androidx.databinding.adapters.TabWidgetBindingAdapter", "method": "setRightStripDrawable", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:textAllCaps": {"android.widget.TextView": {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setAllCaps", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:textColorHighlight": {"android.widget.TextView": {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setHighlightColor", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:textColorHint": {"android.widget.TextView": {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setHintTextColor", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:textColorLink": {"android.widget.TextView": {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setLinkTextColor", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:thumb": {"android.widget.Switch": {"type": "androidx.databinding.adapters.SwitchBindingAdapter", "method": "setThumbDrawable", "requiresOldValue": false, "isStatic": true, "componentClass": null}, "androidx.appcompat.widget.SwitchCompat": {"type": "androidx.databinding.adapters.SwitchCompatBindingAdapter", "method": "setThumbDrawable", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:thumbTint": {"android.widget.AbsSeekBar": {"type": "androidx.databinding.adapters.AbsSeekBarBindingAdapter", "method": "setThumbTintList", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:tint": {"android.widget.ImageView": {"type": "androidx.databinding.adapters.ImageViewBindingAdapter", "method": "setImageTintList", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:tintMode": {"android.widget.ImageView": {"type": "androidx.databinding.adapters.ImageViewBindingAdapter", "method": "setImageTintMode", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:track": {"android.widget.Switch": {"type": "androidx.databinding.adapters.SwitchBindingAdapter", "method": "setTrackDrawable", "requiresOldValue": false, "isStatic": true, "componentClass": null}, "androidx.appcompat.widget.SwitchCompat": {"type": "androidx.databinding.adapters.SwitchCompatBindingAdapter", "method": "setTrackDrawable", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:transformPivotX": {"android.view.View": {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setPivotX", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:transformPivotY": {"android.view.View": {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setPivotY", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "cardCornerRadius": {"androidx.cardview.widget.CardView": {"type": "androidx.databinding.adapters.CardViewBindingAdapter", "method": "setRadius", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "cardMaxElevation": {"androidx.cardview.widget.CardView": {"type": "androidx.databinding.adapters.CardViewBindingAdapter", "method": "setMaxCardElevation", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "cardPreventCornerOverlap": {"androidx.cardview.widget.CardView": {"type": "androidx.databinding.adapters.CardViewBindingAdapter", "method": "setPreventCornerOverlap", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "cardUseCompatPadding": {"androidx.cardview.widget.CardView": {"type": "androidx.databinding.adapters.CardViewBindingAdapter", "method": "setUseCompatPadding", "requiresOldValue": false, "isStatic": true, "componentClass": null}}}, "conversionMethods": {"int": {"android.content.res.ColorStateList": {"type": "androidx.databinding.adapters.Converters", "method": "convertColorToColorStateList", "requiresOldValue": false, "isStatic": true, "componentClass": null}, "android.graphics.drawable.ColorDrawable": {"type": "androidx.databinding.adapters.Converters", "method": "convertColorToDrawable", "requiresOldValue": false, "isStatic": true, "componentClass": null}}}, "untaggableTypes": {"android.view.ViewStub": "androidx.databinding.adapters.ViewStubBindingAdapter"}, "multiValueAdapters": [[{"viewType": "android.view.View", "attributes": ["android:onClick", "android:clickable"], "parameterTypes": ["android.view.View.OnClickListener", "boolean"], "requireAll": true, "attributeIndices": {"android:clickable": 1, "android:onClick": 0}}, {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setOnClick", "requiresOldValue": false, "isStatic": true, "componentClass": null}], [{"viewType": "android.view.View", "attributes": ["android:onClickListener", "android:clickable"], "parameterTypes": ["android.view.View.OnClickListener", "boolean"], "requireAll": true, "attributeIndices": {"android:clickable": 1, "android:onClickListener": 0}}, {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setClickListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}], [{"viewType": "android.view.View", "attributes": ["android:onLongClick", "android:longClickable"], "parameterTypes": ["android.view.View.OnLongClickListener", "boolean"], "requireAll": true, "attributeIndices": {"android:longClickable": 1, "android:onLongClick": 0}}, {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setOnLongClick", "requiresOldValue": false, "isStatic": true, "componentClass": null}], [{"viewType": "android.view.View", "attributes": ["android:onLongClickListener", "android:longClickable"], "parameterTypes": ["android.view.View.OnLongClickListener", "boolean"], "requireAll": true, "attributeIndices": {"android:longClickable": 1, "android:onLongClickListener": 0}}, {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setOnLongClickListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}], [{"viewType": "android.view.View", "attributes": ["android:onViewDetachedFromWindow", "android:onViewAttachedToWindow"], "parameterTypes": ["androidx.databinding.adapters.ViewBindingAdapter.OnViewDetachedFromWindow", "androidx.databinding.adapters.ViewBindingAdapter.OnViewAttachedToWindow"], "requireAll": false, "attributeIndices": {"android:onViewAttachedToWindow": 1, "android:onViewDetachedFromWindow": 0}}, {"type": "androidx.databinding.adapters.ViewBindingAdapter", "method": "setOnAttachStateChangeListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}], [{"viewType": "android.view.ViewGroup", "attributes": ["android:onChildViewAdded", "android:onChildViewRemoved"], "parameterTypes": ["androidx.databinding.adapters.ViewGroupBindingAdapter.OnChildViewAdded", "androidx.databinding.adapters.ViewGroupBindingAdapter.OnChildViewRemoved"], "requireAll": false, "attributeIndices": {"android:onChildViewAdded": 0, "android:onChildViewRemoved": 1}}, {"type": "androidx.databinding.adapters.ViewGroupBindingAdapter", "method": "setListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}], [{"viewType": "android.view.ViewGroup", "attributes": ["android:onAnimationStart", "android:onAnimationEnd", "android:onAnimationRepeat"], "parameterTypes": ["androidx.databinding.adapters.ViewGroupBindingAdapter.OnAnimationStart", "androidx.databinding.adapters.ViewGroupBindingAdapter.OnAnimationEnd", "androidx.databinding.adapters.ViewGroupBindingAdapter.OnAnimationRepeat"], "requireAll": false, "attributeIndices": {"android:onAnimationEnd": 1, "android:onAnimationRepeat": 2, "android:onAnimationStart": 0}}, {"type": "androidx.databinding.adapters.ViewGroupBindingAdapter", "method": "setListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}], [{"viewType": "android.widget.AbsListView", "attributes": ["android:onScroll", "android:onScrollStateChanged"], "parameterTypes": ["androidx.databinding.adapters.AbsListViewBindingAdapter.OnScroll", "androidx.databinding.adapters.AbsListViewBindingAdapter.OnScrollStateChanged"], "requireAll": false, "attributeIndices": {"android:onScroll": 0, "android:onScrollStateChanged": 1}}, {"type": "androidx.databinding.adapters.AbsListViewBindingAdapter", "method": "setOnScroll", "requiresOldValue": false, "isStatic": true, "componentClass": null}], [{"viewType": "android.widget.AdapterView", "attributes": ["android:selectedItemPosition", "android:adapter"], "parameterTypes": ["int", "android.widget.Adapter"], "requireAll": true, "attributeIndices": {"android:adapter": 1, "android:selectedItemPosition": 0}}, {"type": "androidx.databinding.adapters.AdapterViewBindingAdapter", "method": "setSelectedItemPosition", "requiresOldValue": false, "isStatic": true, "componentClass": null}], [{"viewType": "android.widget.AdapterView", "attributes": ["android:selection", "android:adapter"], "parameterTypes": ["int", "android.widget.Adapter"], "requireAll": true, "attributeIndices": {"android:adapter": 1, "android:selection": 0}}, {"type": "androidx.databinding.adapters.AdapterViewBindingAdapter", "method": "setSelection", "requiresOldValue": false, "isStatic": true, "componentClass": null}], [{"viewType": "android.widget.AdapterView", "attributes": ["android:onItemSelected", "android:onNothingSelected", "android:selectedItemPositionAttrChanged"], "parameterTypes": ["androidx.databinding.adapters.AdapterViewBindingAdapter.OnItemSelected", "androidx.databinding.adapters.AdapterViewBindingAdapter.OnNothingSelected", "androidx.databinding.InverseBindingListener"], "requireAll": false, "attributeIndices": {"android:onItemSelected": 0, "android:onNothingSelected": 1, "android:selectedItemPositionAttrChanged": 2}}, {"type": "androidx.databinding.adapters.AdapterViewBindingAdapter", "method": "setOnItemSelectedListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}], [{"viewType": "android.widget.AutoCompleteTextView", "attributes": ["android:fixText", "android:is<PERSON><PERSON><PERSON>"], "parameterTypes": ["androidx.databinding.adapters.AutoCompleteTextViewBindingAdapter.FixText", "androidx.databinding.adapters.AutoCompleteTextViewBindingAdapter.IsValid"], "requireAll": false, "attributeIndices": {"android:fixText": 0, "android:isValid": 1}}, {"type": "androidx.databinding.adapters.AutoCompleteTextViewBindingAdapter", "method": "setValidator", "requiresOldValue": false, "isStatic": true, "componentClass": null}], [{"viewType": "android.widget.AutoCompleteTextView", "attributes": ["android:onItemSelected", "android:onNothingSelected"], "parameterTypes": ["androidx.databinding.adapters.AdapterViewBindingAdapter.OnItemSelected", "androidx.databinding.adapters.AdapterViewBindingAdapter.OnNothingSelected"], "requireAll": false, "attributeIndices": {"android:onItemSelected": 0, "android:onNothingSelected": 1}}, {"type": "androidx.databinding.adapters.AutoCompleteTextViewBindingAdapter", "method": "setOnItemSelectedListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}], [{"viewType": "android.widget.CalendarView", "attributes": ["android:onSelectedDayChange", "android:dateAttrChanged"], "parameterTypes": ["android.widget.CalendarView.OnDateChangeListener", "androidx.databinding.InverseBindingListener"], "requireAll": false, "attributeIndices": {"android:dateAttrChanged": 1, "android:onSelectedDayChange": 0}}, {"type": "androidx.databinding.adapters.CalendarViewBindingAdapter", "method": "setListeners", "requiresOldValue": false, "isStatic": true, "componentClass": null}], [{"viewType": "android.widget.CompoundButton", "attributes": ["android:onCheckedChanged", "android:<PERSON><PERSON>ttr<PERSON><PERSON><PERSON>"], "parameterTypes": ["android.widget.CompoundButton.OnCheckedChangeListener", "androidx.databinding.InverseBindingListener"], "requireAll": false, "attributeIndices": {"android:checkedAttrChanged": 1, "android:onCheckedChanged": 0}}, {"type": "androidx.databinding.adapters.CompoundButtonBindingAdapter", "method": "setListeners", "requiresOldValue": false, "isStatic": true, "componentClass": null}], [{"viewType": "android.widget.DatePicker", "attributes": ["android:year", "android:month", "android:day", "android:onDateChanged", "android:year<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "android:month<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "android:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "parameterTypes": ["int", "int", "int", "android.widget.DatePicker.OnDateChangedListener", "androidx.databinding.InverseBindingListener", "androidx.databinding.InverseBindingListener", "androidx.databinding.InverseBindingListener"], "requireAll": false, "attributeIndices": {"android:day": 2, "android:dayAttrChanged": 6, "android:month": 1, "android:monthAttrChanged": 5, "android:onDateChanged": 3, "android:year": 0, "android:yearAttrChanged": 4}}, {"type": "androidx.databinding.adapters.DatePickerBindingAdapter", "method": "setListeners", "requiresOldValue": false, "isStatic": true, "componentClass": null}], [{"viewType": "android.widget.NumberPicker", "attributes": ["android:onValueChange", "android:valueAttrChanged"], "parameterTypes": ["android.widget.NumberPicker.OnValueChangeListener", "androidx.databinding.InverseBindingListener"], "requireAll": false, "attributeIndices": {"android:onValueChange": 0, "android:valueAttrChanged": 1}}, {"type": "androidx.databinding.adapters.NumberPickerBindingAdapter", "method": "setListeners", "requiresOldValue": false, "isStatic": true, "componentClass": null}], [{"viewType": "android.widget.RadioGroup", "attributes": ["android:onCheckedChanged", "android:checkedButtonAttrChanged"], "parameterTypes": ["android.widget.RadioGroup.OnCheckedChangeListener", "androidx.databinding.InverseBindingListener"], "requireAll": false, "attributeIndices": {"android:checkedButtonAttrChanged": 1, "android:onCheckedChanged": 0}}, {"type": "androidx.databinding.adapters.RadioGroupBindingAdapter", "method": "setListeners", "requiresOldValue": false, "isStatic": true, "componentClass": null}], [{"viewType": "android.widget.RatingBar", "attributes": ["android:onRatingChanged", "android:ratingAttrChanged"], "parameterTypes": ["android.widget.RatingBar.OnRatingBarChangeListener", "androidx.databinding.InverseBindingListener"], "requireAll": false, "attributeIndices": {"android:onRatingChanged": 0, "android:ratingAttrChanged": 1}}, {"type": "androidx.databinding.adapters.RatingBarBindingAdapter", "method": "setListeners", "requiresOldValue": false, "isStatic": true, "componentClass": null}], [{"viewType": "android.widget.SearchView", "attributes": ["android:onQueryTextSubmit", "android:onQueryTextChange"], "parameterTypes": ["androidx.databinding.adapters.SearchViewBindingAdapter.OnQueryTextSubmit", "androidx.databinding.adapters.SearchViewBindingAdapter.OnQueryTextChange"], "requireAll": false, "attributeIndices": {"android:onQueryTextChange": 1, "android:onQueryTextSubmit": 0}}, {"type": "androidx.databinding.adapters.SearchViewBindingAdapter", "method": "setOnQueryTextListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}], [{"viewType": "android.widget.SearchView", "attributes": ["android:onSuggestionSelect", "android:onSuggestionClick"], "parameterTypes": ["androidx.databinding.adapters.SearchViewBindingAdapter.OnSuggestionSelect", "androidx.databinding.adapters.SearchViewBindingAdapter.OnSuggestionClick"], "requireAll": false, "attributeIndices": {"android:onSuggestionClick": 1, "android:onSuggestionSelect": 0}}, {"type": "androidx.databinding.adapters.SearchViewBindingAdapter", "method": "setOnSuggestListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}], [{"viewType": "android.widget.SeekBar", "attributes": ["android:onStartTrackingTouch", "android:onStopTrackingTouch", "android:onProgressChanged", "android:progressAttrChanged"], "parameterTypes": ["androidx.databinding.adapters.SeekBarBindingAdapter.OnStartTrackingTouch", "androidx.databinding.adapters.SeekBarBindingAdapter.OnStopTrackingTouch", "androidx.databinding.adapters.SeekBarBindingAdapter.OnProgressChanged", "androidx.databinding.InverseBindingListener"], "requireAll": false, "attributeIndices": {"android:onProgressChanged": 2, "android:onStartTrackingTouch": 0, "android:onStopTrackingTouch": 1, "android:progressAttrChanged": 3}}, {"type": "androidx.databinding.adapters.SeekBarBindingAdapter", "method": "setOnSeekBarChangeListener", "requiresOldValue": false, "isStatic": true, "componentClass": null}], [{"viewType": "android.widget.TabHost", "attributes": ["android:onTabChanged", "android:currentTabAttrChanged"], "parameterTypes": ["android.widget.TabHost.OnTabChangeListener", "androidx.databinding.InverseBindingListener"], "requireAll": false, "attributeIndices": {"android:currentTabAttrChanged": 1, "android:onTabChanged": 0}}, {"type": "androidx.databinding.adapters.TabHostBindingAdapter", "method": "setListeners", "requiresOldValue": false, "isStatic": true, "componentClass": null}], [{"viewType": "android.widget.TextView", "attributes": ["android:beforeTextChanged", "android:onTextChanged", "android:afterTextChanged", "android:textAttrChanged"], "parameterTypes": ["androidx.databinding.adapters.TextViewBindingAdapter.BeforeTextChanged", "androidx.databinding.adapters.TextViewBindingAdapter.OnTextChanged", "androidx.databinding.adapters.TextViewBindingAdapter.AfterTextChanged", "androidx.databinding.InverseBindingListener"], "requireAll": false, "attributeIndices": {"android:afterTextChanged": 2, "android:beforeTextChanged": 0, "android:onTextChanged": 1, "android:textAttrChanged": 3}}, {"type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "setTextWatcher", "requiresOldValue": false, "isStatic": true, "componentClass": null}], [{"viewType": "android.widget.TimePicker", "attributes": ["android:onTimeChanged", "android:hour<PERSON>ttr<PERSON><PERSON>ed", "android:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "parameterTypes": ["android.widget.TimePicker.OnTimeChangedListener", "androidx.databinding.InverseBindingListener", "androidx.databinding.InverseBindingListener"], "requireAll": false, "attributeIndices": {"android:hourAttrChanged": 1, "android:minuteAttrChanged": 2, "android:onTimeChanged": 0}}, {"type": "androidx.databinding.adapters.TimePickerBindingAdapter", "method": "setListeners", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "inverseAdapters": {"android:currentTab": [[{"viewType": "android.widget.TabHost", "valueType": "int"}, {"event": "android:currentTabAttrChanged", "type": "androidx.databinding.adapters.TabHostBindingAdapter", "method": "getCurrentTab", "requiresOldValue": false, "isStatic": true, "componentClass": null}], [{"viewType": "android.widget.TabHost", "valueType": "java.lang.String"}, {"event": "android:currentTabAttrChanged", "type": "androidx.databinding.adapters.TabHostBindingAdapter", "method": "getCurrentTabTag", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:hour": [[{"viewType": "android.widget.TimePicker", "valueType": "int"}, {"event": "android:hour<PERSON>ttr<PERSON><PERSON>ed", "type": "androidx.databinding.adapters.TimePickerBindingAdapter", "method": "getHour", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:minute": [[{"viewType": "android.widget.TimePicker", "valueType": "int"}, {"event": "android:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "androidx.databinding.adapters.TimePickerBindingAdapter", "method": "getMinute", "requiresOldValue": false, "isStatic": true, "componentClass": null}]], "android:text": [[{"viewType": "android.widget.TextView", "valueType": "java.lang.String"}, {"event": "android:textAttrChanged", "type": "androidx.databinding.adapters.TextViewBindingAdapter", "method": "getTextString", "requiresOldValue": false, "isStatic": true, "componentClass": null}]]}, "inverseMethods": {"android:checked": {"android.widget.CompoundButton": {"event": "android:<PERSON><PERSON>ttr<PERSON><PERSON><PERSON>", "type": "androidx.databinding.adapters.CompoundButtonBindingAdapter", "method": "", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:checkedButton": {"android.widget.RadioGroup": {"event": "android:checkedButtonAttrChanged", "type": "androidx.databinding.adapters.RadioGroupBindingAdapter", "method": "getCheckedRadioButtonId", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:date": {"android.widget.CalendarView": {"event": "android:dateAttrChanged", "type": "androidx.databinding.adapters.CalendarViewBindingAdapter", "method": "", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:day": {"android.widget.DatePicker": {"event": "android:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "androidx.databinding.adapters.DatePickerBindingAdapter", "method": "getDayOfMonth", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:month": {"android.widget.DatePicker": {"event": "android:month<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "androidx.databinding.adapters.DatePickerBindingAdapter", "method": "", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:progress": {"android.widget.SeekBar": {"event": "android:progressAttrChanged", "type": "androidx.databinding.adapters.SeekBarBindingAdapter", "method": "", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:rating": {"android.widget.RatingBar": {"event": "android:ratingAttrChanged", "type": "androidx.databinding.adapters.RatingBarBindingAdapter", "method": "", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:selectedItemPosition": {"android.widget.AdapterView": {"event": "android:selectedItemPositionAttrChanged", "type": "androidx.databinding.adapters.AdapterViewBindingAdapter", "method": "", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:selection": {"android.widget.AdapterView": {"event": "android:selectedItemPositionAttrChanged", "type": "androidx.databinding.adapters.AdapterViewBindingAdapter", "method": "getSelectedItemPosition", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:value": {"android.widget.NumberPicker": {"event": "android:valueAttrChanged", "type": "androidx.databinding.adapters.NumberPickerBindingAdapter", "method": "", "requiresOldValue": false, "isStatic": true, "componentClass": null}}, "android:year": {"android.widget.DatePicker": {"event": "android:year<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "androidx.databinding.adapters.DatePickerBindingAdapter", "method": "", "requiresOldValue": false, "isStatic": true, "componentClass": null}}}, "twoWayMethods": {}, "useAndroidX": true}