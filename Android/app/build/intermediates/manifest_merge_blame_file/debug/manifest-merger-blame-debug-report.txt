1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.voipforwarder.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <!-- Required permissions for call detection and VoIP functionality -->
12    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
12-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:6:5-75
12-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:6:22-72
13    <uses-permission android:name="android.permission.READ_CALL_LOG" />
13-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:7:5-72
13-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:7:22-69
14    <uses-permission android:name="android.permission.ANSWER_PHONE_CALLS" />
14-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:8:5-77
14-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:8:22-74
15    <uses-permission android:name="android.permission.RECORD_AUDIO" />
15-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:9:5-71
15-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:9:22-68
16    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
16-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:10:5-80
16-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:10:22-77
17    <uses-permission android:name="android.permission.MANAGE_OWN_CALLS" />
17-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:11:5-75
17-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:11:22-72
18    <uses-permission android:name="android.permission.CALL_PHONE" />
18-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:12:5-69
18-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:12:22-66
19
20    <!-- Network permissions for VoIP -->
21    <uses-permission android:name="android.permission.INTERNET" />
21-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:15:5-67
21-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:15:22-64
22    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
22-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:16:5-79
22-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:16:22-76
23    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
23-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:17:5-76
23-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:17:22-73
24    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
24-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:18:5-76
24-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:18:22-73
25
26    <!-- Background service permissions -->
27    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
27-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:21:5-77
27-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:21:22-74
28    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_PHONE_CALL" />
28-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:22:5-88
28-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:22:22-85
29    <uses-permission android:name="android.permission.WAKE_LOCK" />
29-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:23:5-68
29-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:23:22-65
30    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
30-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:24:5-78
30-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:24:22-75
31
32    <!-- Boot receiver permission -->
33    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
33-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:27:5-81
33-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:27:22-78
34
35    <!-- Storage permissions for call logs -->
36    <uses-permission
36-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:30:5-31:38
37        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
37-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:30:22-78
38        android:maxSdkVersion="28" />
38-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:31:9-35
39
40    <!-- Required features -->
41    <uses-feature
41-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:34:5-36:35
42        android:name="android.hardware.telephony"
42-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:35:9-50
43        android:required="true" />
43-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:36:9-32
44    <uses-feature
44-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:37:5-39:35
45        android:name="android.hardware.microphone"
45-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:38:9-51
46        android:required="true" />
46-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:39:9-32
47
48    <permission
48-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/2e37c0ed8bc86c66b0dc0e6a1f68ae10/transformed/core-1.12.0/AndroidManifest.xml:22:5-24:47
49        android:name="com.voipforwarder.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
49-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/2e37c0ed8bc86c66b0dc0e6a1f68ae10/transformed/core-1.12.0/AndroidManifest.xml:23:9-81
50        android:protectionLevel="signature" />
50-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/2e37c0ed8bc86c66b0dc0e6a1f68ae10/transformed/core-1.12.0/AndroidManifest.xml:24:9-44
51
52    <uses-permission android:name="com.voipforwarder.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- Needed to be able to use WifiManager.MulticastLock -->
52-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/2e37c0ed8bc86c66b0dc0e6a1f68ae10/transformed/core-1.12.0/AndroidManifest.xml:26:5-97
52-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/2e37c0ed8bc86c66b0dc0e6a1f68ae10/transformed/core-1.12.0/AndroidManifest.xml:26:22-94
53    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
53-->[org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:16:5-86
53-->[org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:16:22-83
54    <uses-permission android:name="android.permission.CAMERA" /> <!-- Needed to allow Linphone to install on tablets, since android.permission.CAMERA implies android.hardware.camera and android.hardware.camera.autofocus are required -->
54-->[org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:17:5-65
54-->[org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:17:22-62
55    <uses-feature
55-->[org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:19:5-21:36
56        android:name="android.hardware.camera"
56-->[org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:20:9-47
57        android:required="false" /> <!-- Needed for bluetooth -->
57-->[org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:21:9-33
58    <uses-permission android:name="android.permission.BLUETOOTH" /> <!-- To vibrate while incoming call -->
58-->[org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:23:5-68
58-->[org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:23:22-65
59    <uses-permission android:name="android.permission.VIBRATE" /> <!-- Needed to check current do not disturb policy -->
59-->[org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:27:5-66
59-->[org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:27:22-63
60    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
60-->[org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:29:5-85
60-->[org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:29:22-82
61
62    <application
62-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:41:5-118:19
63        android:name="com.voipforwarder.app.VoIPForwarderApplication"
63-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:42:9-49
64        android:allowBackup="true"
64-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:43:9-35
65        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
65-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/2e37c0ed8bc86c66b0dc0e6a1f68ae10/transformed/core-1.12.0/AndroidManifest.xml:28:18-86
66        android:dataExtractionRules="@xml/data_extraction_rules"
66-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:44:9-65
67        android:debuggable="true"
68        android:extractNativeLibs="false"
69        android:fullBackupContent="@xml/backup_rules"
69-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:45:9-54
70        android:icon="@mipmap/ic_launcher"
70-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:46:9-43
71        android:label="@string/app_name"
71-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:47:9-41
72        android:roundIcon="@mipmap/ic_launcher_round"
72-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:48:9-54
73        android:supportsRtl="true"
73-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:49:9-35
74        android:theme="@style/Theme.VoIPCallForwarder" >
74-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:50:9-55
75
76        <!-- Main Activity -->
77        <activity
77-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:54:9-63:20
78            android:name="com.voipforwarder.app.ui.MainActivity"
78-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:55:13-44
79            android:exported="true"
79-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:56:13-36
80            android:launchMode="singleTop"
80-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:57:13-43
81            android:theme="@style/Theme.VoIPCallForwarder" >
81-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:58:13-59
82            <intent-filter>
82-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:59:13-62:29
83                <action android:name="android.intent.action.MAIN" />
83-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:60:17-69
83-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:60:25-66
84
85                <category android:name="android.intent.category.LAUNCHER" />
85-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:61:17-77
85-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:61:27-74
86            </intent-filter>
87        </activity>
88
89        <!-- Call Forwarding Service -->
90        <service
90-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:66:9-70:57
91            android:name="com.voipforwarder.app.service.CallForwardingService"
91-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:67:13-58
92            android:enabled="true"
92-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:68:13-35
93            android:exported="false"
93-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:69:13-37
94            android:foregroundServiceType="phoneCall" />
94-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:70:13-54
95
96        <!-- VoIP Service -->
97        <service
97-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:73:9-77:57
98            android:name="com.voipforwarder.app.service.VoIPService"
98-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:74:13-48
99            android:enabled="true"
99-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:75:13-35
100            android:exported="false"
100-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:76:13-37
101            android:foregroundServiceType="phoneCall" />
101-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:77:13-54
102
103        <!-- Call Screening Service (Android 10+) -->
104        <service
104-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:80:9-87:19
105            android:name="com.voipforwarder.app.service.CallScreeningServiceImpl"
105-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:81:13-61
106            android:exported="true"
106-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:83:13-36
107            android:permission="android.permission.BIND_SCREENING_SERVICE" >
107-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:82:13-75
108            <intent-filter>
108-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:84:13-86:29
109                <action android:name="android.telecom.CallScreeningService" />
109-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:85:17-79
109-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:85:25-76
110            </intent-filter>
111        </service>
112
113        <!-- Phone State Receiver -->
114        <receiver
114-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:90:9-97:20
115            android:name="com.voipforwarder.app.receiver.PhoneStateReceiver"
115-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:91:13-56
116            android:enabled="true"
116-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:92:13-35
117            android:exported="true" >
117-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:93:13-36
118            <intent-filter android:priority="1000" >
118-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:94:13-96:29
118-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:94:28-51
119                <action android:name="android.intent.action.PHONE_STATE" />
119-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:95:17-76
119-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:95:25-73
120            </intent-filter>
121        </receiver>
122
123        <!-- Boot Receiver -->
124        <receiver
124-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:100:9-110:20
125            android:name="com.voipforwarder.app.receiver.BootReceiver"
125-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:101:13-50
126            android:enabled="true"
126-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:102:13-35
127            android:exported="true" >
127-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:103:13-36
128            <intent-filter android:priority="1000" >
128-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:104:13-109:29
128-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:104:28-51
129                <action android:name="android.intent.action.BOOT_COMPLETED" />
129-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:105:17-79
129-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:105:25-76
130                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
130-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:106:17-84
130-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:106:25-81
131                <action android:name="android.intent.action.PACKAGE_REPLACED" />
131-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:107:17-81
131-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:107:25-78
132
133                <data android:scheme="package" />
133-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:108:17-50
133-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:108:23-47
134            </intent-filter>
135        </receiver>
136
137        <!-- Call Log Observer Service -->
138        <service
138-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:113:9-116:40
139            android:name="com.voipforwarder.app.service.CallLogObserverService"
139-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:114:13-59
140            android:enabled="true"
140-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:115:13-35
141            android:exported="false" />
141-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:116:13-37
142
143        <provider
143-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/a30e1fe6c4ed5ace2aadb0cb60127282/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:24:9-32:20
144            android:name="androidx.startup.InitializationProvider"
144-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/a30e1fe6c4ed5ace2aadb0cb60127282/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:25:13-67
145            android:authorities="com.voipforwarder.app.androidx-startup"
145-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/a30e1fe6c4ed5ace2aadb0cb60127282/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:26:13-68
146            android:exported="false" >
146-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/a30e1fe6c4ed5ace2aadb0cb60127282/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:27:13-37
147            <meta-data
147-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/a30e1fe6c4ed5ace2aadb0cb60127282/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:29:13-31:52
148                android:name="androidx.emoji2.text.EmojiCompatInitializer"
148-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/a30e1fe6c4ed5ace2aadb0cb60127282/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:30:17-75
149                android:value="androidx.startup" />
149-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/a30e1fe6c4ed5ace2aadb0cb60127282/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:31:17-49
150            <meta-data
150-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:34:13-36:52
151                android:name="androidx.work.WorkManagerInitializer"
151-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:35:17-68
152                android:value="androidx.startup" />
152-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:36:17-49
153            <meta-data
153-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/6bad45c3e73cba33f913594ccdebc03b/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
154                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
154-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/6bad45c3e73cba33f913594ccdebc03b/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
155                android:value="androidx.startup" />
155-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/6bad45c3e73cba33f913594ccdebc03b/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
156            <meta-data
156-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:29:13-31:52
157                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
157-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:30:17-85
158                android:value="androidx.startup" />
158-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:31:17-49
159        </provider>
160
161        <service
161-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:39:9-45:35
162            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
162-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:40:13-88
163            android:directBootAware="false"
163-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:41:13-44
164            android:enabled="@bool/enable_system_alarm_service_default"
164-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:42:13-72
165            android:exported="false" />
165-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:43:13-37
166        <service
166-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:46:9-52:35
167            android:name="androidx.work.impl.background.systemjob.SystemJobService"
167-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:47:13-84
168            android:directBootAware="false"
168-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:48:13-44
169            android:enabled="@bool/enable_system_job_service_default"
169-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:49:13-70
170            android:exported="true"
170-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:50:13-36
171            android:permission="android.permission.BIND_JOB_SERVICE" />
171-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:51:13-69
172        <service
172-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:53:9-59:35
173            android:name="androidx.work.impl.foreground.SystemForegroundService"
173-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:54:13-81
174            android:directBootAware="false"
174-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:55:13-44
175            android:enabled="@bool/enable_system_foreground_service_default"
175-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:56:13-77
176            android:exported="false" />
176-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:57:13-37
177
178        <receiver
178-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:61:9-66:35
179            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
179-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:62:13-88
180            android:directBootAware="false"
180-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:63:13-44
181            android:enabled="true"
181-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:64:13-35
182            android:exported="false" />
182-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:65:13-37
183        <receiver
183-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:67:9-77:20
184            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
184-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:68:13-106
185            android:directBootAware="false"
185-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:69:13-44
186            android:enabled="false"
186-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:70:13-36
187            android:exported="false" >
187-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:71:13-37
188            <intent-filter>
188-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:73:13-76:29
189                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
189-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:74:17-87
189-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:74:25-84
190                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
190-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:75:17-90
190-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:75:25-87
191            </intent-filter>
192        </receiver>
193        <receiver
193-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:78:9-88:20
194            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
194-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:79:13-104
195            android:directBootAware="false"
195-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:80:13-44
196            android:enabled="false"
196-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:81:13-36
197            android:exported="false" >
197-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:82:13-37
198            <intent-filter>
198-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:84:13-87:29
199                <action android:name="android.intent.action.BATTERY_OKAY" />
199-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:85:17-77
199-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:85:25-74
200                <action android:name="android.intent.action.BATTERY_LOW" />
200-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:86:17-76
200-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:86:25-73
201            </intent-filter>
202        </receiver>
203        <receiver
203-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:89:9-99:20
204            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
204-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:90:13-104
205            android:directBootAware="false"
205-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:91:13-44
206            android:enabled="false"
206-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:92:13-36
207            android:exported="false" >
207-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:93:13-37
208            <intent-filter>
208-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:95:13-98:29
209                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
209-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:96:17-83
209-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:96:25-80
210                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
210-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:97:17-82
210-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:97:25-79
211            </intent-filter>
212        </receiver>
213        <receiver
213-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:100:9-109:20
214            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
214-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:101:13-103
215            android:directBootAware="false"
215-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:102:13-44
216            android:enabled="false"
216-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:103:13-36
217            android:exported="false" >
217-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:104:13-37
218            <intent-filter>
218-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:106:13-108:29
219                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
219-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:107:17-79
219-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:107:25-76
220            </intent-filter>
221        </receiver>
222        <receiver
222-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:110:9-121:20
223            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
223-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:111:13-88
224            android:directBootAware="false"
224-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:112:13-44
225            android:enabled="false"
225-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:113:13-36
226            android:exported="false" >
226-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:114:13-37
227            <intent-filter>
227-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:116:13-120:29
228                <action android:name="android.intent.action.BOOT_COMPLETED" />
228-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:105:17-79
228-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:105:25-76
229                <action android:name="android.intent.action.TIME_SET" />
229-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:118:17-73
229-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:118:25-70
230                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
230-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:119:17-81
230-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:119:25-78
231            </intent-filter>
232        </receiver>
233        <receiver
233-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:122:9-131:20
234            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
234-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:123:13-99
235            android:directBootAware="false"
235-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:124:13-44
236            android:enabled="@bool/enable_system_alarm_service_default"
236-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:125:13-72
237            android:exported="false" >
237-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:126:13-37
238            <intent-filter>
238-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:128:13-130:29
239                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
239-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:129:17-98
239-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:129:25-95
240            </intent-filter>
241        </receiver>
242        <receiver
242-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:132:9-142:20
243            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
243-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:133:13-78
244            android:directBootAware="false"
244-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:134:13-44
245            android:enabled="true"
245-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:135:13-35
246            android:exported="true"
246-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:136:13-36
247            android:permission="android.permission.DUMP" >
247-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:137:13-57
248            <intent-filter>
248-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:139:13-141:29
249                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
249-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:140:17-88
249-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:140:25-85
250            </intent-filter>
251        </receiver>
252
253        <service
253-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/8d1765fc745ef3335caadcdc97a01918/transformed/room-runtime-2.6.1/AndroidManifest.xml:24:9-28:63
254            android:name="androidx.room.MultiInstanceInvalidationService"
254-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/8d1765fc745ef3335caadcdc97a01918/transformed/room-runtime-2.6.1/AndroidManifest.xml:25:13-74
255            android:directBootAware="true"
255-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/8d1765fc745ef3335caadcdc97a01918/transformed/room-runtime-2.6.1/AndroidManifest.xml:26:13-43
256            android:exported="false" />
256-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/8d1765fc745ef3335caadcdc97a01918/transformed/room-runtime-2.6.1/AndroidManifest.xml:27:13-37
257
258        <receiver
258-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:34:9-52:20
259            android:name="androidx.profileinstaller.ProfileInstallReceiver"
259-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:35:13-76
260            android:directBootAware="false"
260-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:36:13-44
261            android:enabled="true"
261-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:37:13-35
262            android:exported="true"
262-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:38:13-36
263            android:permission="android.permission.DUMP" >
263-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:39:13-57
264            <intent-filter>
264-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:40:13-42:29
265                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
265-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:41:17-91
265-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:41:25-88
266            </intent-filter>
267            <intent-filter>
267-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:43:13-45:29
268                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
268-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:44:17-85
268-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:44:25-82
269            </intent-filter>
270            <intent-filter>
270-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:46:13-48:29
271                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
271-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:47:17-88
271-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:47:25-85
272            </intent-filter>
273            <intent-filter>
273-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:49:13-51:29
274                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
274-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:50:17-95
274-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:50:25-92
275            </intent-filter>
276        </receiver>
277    </application>
278
279</manifest>
