1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.voipforwarder.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <!-- Required permissions for call detection and VoIP functionality -->
12    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
12-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:6:5-75
12-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:6:22-72
13    <uses-permission android:name="android.permission.READ_CALL_LOG" />
13-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:7:5-72
13-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:7:22-69
14    <uses-permission android:name="android.permission.ANSWER_PHONE_CALLS" />
14-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:8:5-77
14-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:8:22-74
15    <uses-permission android:name="android.permission.RECORD_AUDIO" />
15-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:9:5-71
15-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:9:22-68
16    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
16-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:10:5-80
16-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:10:22-77
17    <uses-permission android:name="android.permission.MANAGE_OWN_CALLS" />
17-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:11:5-75
17-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:11:22-72
18    <uses-permission android:name="android.permission.CALL_PHONE" />
18-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:12:5-69
18-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:12:22-66
19
20    <!-- Network permissions for VoIP -->
21    <uses-permission android:name="android.permission.INTERNET" />
21-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:15:5-67
21-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:15:22-64
22    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
22-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:16:5-79
22-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:16:22-76
23    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
23-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:17:5-76
23-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:17:22-73
24    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
24-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:18:5-76
24-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:18:22-73
25
26    <!-- Background service permissions -->
27    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
27-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:21:5-77
27-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:21:22-74
28    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_PHONE_CALL" />
28-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:22:5-88
28-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:22:22-85
29    <uses-permission android:name="android.permission.WAKE_LOCK" />
29-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:23:5-68
29-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:23:22-65
30    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
30-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:24:5-78
30-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:24:22-75
31
32    <!-- Boot receiver permission -->
33    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
33-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:27:5-81
33-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:27:22-78
34
35    <!-- Storage permissions for call logs -->
36    <uses-permission
36-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:30:5-31:38
37        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
37-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:30:22-78
38        android:maxSdkVersion="28" />
38-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:31:9-35
39
40    <!-- Required features -->
41    <uses-feature
41-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:34:5-36:35
42        android:name="android.hardware.telephony"
42-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:35:9-50
43        android:required="true" />
43-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:36:9-32
44    <uses-feature
44-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:37:5-39:35
45        android:name="android.hardware.microphone"
45-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:38:9-51
46        android:required="true" />
46-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:39:9-32
47
48    <permission
48-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/2e37c0ed8bc86c66b0dc0e6a1f68ae10/transformed/core-1.12.0/AndroidManifest.xml:22:5-24:47
49        android:name="com.voipforwarder.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
49-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/2e37c0ed8bc86c66b0dc0e6a1f68ae10/transformed/core-1.12.0/AndroidManifest.xml:23:9-81
50        android:protectionLevel="signature" />
50-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/2e37c0ed8bc86c66b0dc0e6a1f68ae10/transformed/core-1.12.0/AndroidManifest.xml:24:9-44
51
52    <uses-permission android:name="com.voipforwarder.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- Needed to be able to use WifiManager.MulticastLock -->
52-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/2e37c0ed8bc86c66b0dc0e6a1f68ae10/transformed/core-1.12.0/AndroidManifest.xml:26:5-97
52-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/2e37c0ed8bc86c66b0dc0e6a1f68ae10/transformed/core-1.12.0/AndroidManifest.xml:26:22-94
53    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
53-->[org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:16:5-86
53-->[org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:16:22-83
54    <uses-permission android:name="android.permission.CAMERA" /> <!-- Needed to allow Linphone to install on tablets, since android.permission.CAMERA implies android.hardware.camera and android.hardware.camera.autofocus are required -->
54-->[org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:17:5-65
54-->[org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:17:22-62
55    <uses-feature
55-->[org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:19:5-21:36
56        android:name="android.hardware.camera"
56-->[org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:20:9-47
57        android:required="false" /> <!-- Needed for bluetooth -->
57-->[org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:21:9-33
58    <uses-permission android:name="android.permission.BLUETOOTH" /> <!-- To vibrate while incoming call -->
58-->[org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:23:5-68
58-->[org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:23:22-65
59    <uses-permission android:name="android.permission.VIBRATE" /> <!-- Needed to check current do not disturb policy -->
59-->[org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:27:5-66
59-->[org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:27:22-63
60    <uses-permission android:name="android.permission.ACCESS_NOTIFICATION_POLICY" />
60-->[org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:29:5-85
60-->[org.linphone:linphone-sdk-android:5.2.7] /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/AndroidManifest.xml:29:22-82
61
62    <application
62-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:41:5-118:19
63        android:name="com.voipforwarder.app.VoIPForwarderApplication"
63-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:42:9-49
64        android:allowBackup="true"
64-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:43:9-35
65        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
65-->[androidx.core:core:1.12.0] /Users/<USER>/.gradle/caches/8.10/transforms/2e37c0ed8bc86c66b0dc0e6a1f68ae10/transformed/core-1.12.0/AndroidManifest.xml:28:18-86
66        android:dataExtractionRules="@xml/data_extraction_rules"
66-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:44:9-65
67        android:extractNativeLibs="false"
68        android:fullBackupContent="@xml/backup_rules"
68-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:45:9-54
69        android:icon="@mipmap/ic_launcher"
69-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:46:9-43
70        android:label="@string/app_name"
70-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:47:9-41
71        android:roundIcon="@mipmap/ic_launcher_round"
71-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:48:9-54
72        android:supportsRtl="true"
72-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:49:9-35
73        android:theme="@style/Theme.VoIPCallForwarder" >
73-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:50:9-55
74
75        <!-- Main Activity -->
76        <activity
76-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:54:9-63:20
77            android:name="com.voipforwarder.app.ui.MainActivity"
77-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:55:13-44
78            android:exported="true"
78-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:56:13-36
79            android:launchMode="singleTop"
79-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:57:13-43
80            android:theme="@style/Theme.VoIPCallForwarder" >
80-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:58:13-59
81            <intent-filter>
81-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:59:13-62:29
82                <action android:name="android.intent.action.MAIN" />
82-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:60:17-69
82-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:60:25-66
83
84                <category android:name="android.intent.category.LAUNCHER" />
84-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:61:17-77
84-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:61:27-74
85            </intent-filter>
86        </activity>
87
88        <!-- Call Forwarding Service -->
89        <service
89-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:66:9-70:57
90            android:name="com.voipforwarder.app.service.CallForwardingService"
90-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:67:13-58
91            android:enabled="true"
91-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:68:13-35
92            android:exported="false"
92-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:69:13-37
93            android:foregroundServiceType="phoneCall" />
93-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:70:13-54
94
95        <!-- VoIP Service -->
96        <service
96-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:73:9-77:57
97            android:name="com.voipforwarder.app.service.VoIPService"
97-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:74:13-48
98            android:enabled="true"
98-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:75:13-35
99            android:exported="false"
99-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:76:13-37
100            android:foregroundServiceType="phoneCall" />
100-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:77:13-54
101
102        <!-- Call Screening Service (Android 10+) -->
103        <service
103-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:80:9-87:19
104            android:name="com.voipforwarder.app.service.CallScreeningServiceImpl"
104-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:81:13-61
105            android:exported="true"
105-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:83:13-36
106            android:permission="android.permission.BIND_SCREENING_SERVICE" >
106-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:82:13-75
107            <intent-filter>
107-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:84:13-86:29
108                <action android:name="android.telecom.CallScreeningService" />
108-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:85:17-79
108-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:85:25-76
109            </intent-filter>
110        </service>
111
112        <!-- Phone State Receiver -->
113        <receiver
113-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:90:9-97:20
114            android:name="com.voipforwarder.app.receiver.PhoneStateReceiver"
114-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:91:13-56
115            android:enabled="true"
115-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:92:13-35
116            android:exported="true" >
116-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:93:13-36
117            <intent-filter android:priority="1000" >
117-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:94:13-96:29
117-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:94:28-51
118                <action android:name="android.intent.action.PHONE_STATE" />
118-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:95:17-76
118-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:95:25-73
119            </intent-filter>
120        </receiver>
121
122        <!-- Boot Receiver -->
123        <receiver
123-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:100:9-110:20
124            android:name="com.voipforwarder.app.receiver.BootReceiver"
124-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:101:13-50
125            android:enabled="true"
125-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:102:13-35
126            android:exported="true" >
126-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:103:13-36
127            <intent-filter android:priority="1000" >
127-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:104:13-109:29
127-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:104:28-51
128                <action android:name="android.intent.action.BOOT_COMPLETED" />
128-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:105:17-79
128-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:105:25-76
129                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
129-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:106:17-84
129-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:106:25-81
130                <action android:name="android.intent.action.PACKAGE_REPLACED" />
130-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:107:17-81
130-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:107:25-78
131
132                <data android:scheme="package" />
132-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:108:17-50
132-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:108:23-47
133            </intent-filter>
134        </receiver>
135
136        <!-- Call Log Observer Service -->
137        <service
137-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:113:9-116:40
138            android:name="com.voipforwarder.app.service.CallLogObserverService"
138-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:114:13-59
139            android:enabled="true"
139-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:115:13-35
140            android:exported="false" />
140-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:116:13-37
141
142        <provider
142-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/a30e1fe6c4ed5ace2aadb0cb60127282/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:24:9-32:20
143            android:name="androidx.startup.InitializationProvider"
143-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/a30e1fe6c4ed5ace2aadb0cb60127282/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:25:13-67
144            android:authorities="com.voipforwarder.app.androidx-startup"
144-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/a30e1fe6c4ed5ace2aadb0cb60127282/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:26:13-68
145            android:exported="false" >
145-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/a30e1fe6c4ed5ace2aadb0cb60127282/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:27:13-37
146            <meta-data
146-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/a30e1fe6c4ed5ace2aadb0cb60127282/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:29:13-31:52
147                android:name="androidx.emoji2.text.EmojiCompatInitializer"
147-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/a30e1fe6c4ed5ace2aadb0cb60127282/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:30:17-75
148                android:value="androidx.startup" />
148-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.10/transforms/a30e1fe6c4ed5ace2aadb0cb60127282/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:31:17-49
149            <meta-data
149-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:34:13-36:52
150                android:name="androidx.work.WorkManagerInitializer"
150-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:35:17-68
151                android:value="androidx.startup" />
151-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:36:17-49
152            <meta-data
152-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/6bad45c3e73cba33f913594ccdebc03b/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
153                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
153-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/6bad45c3e73cba33f913594ccdebc03b/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
154                android:value="androidx.startup" />
154-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.10/transforms/6bad45c3e73cba33f913594ccdebc03b/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
155            <meta-data
155-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:29:13-31:52
156                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
156-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:30:17-85
157                android:value="androidx.startup" />
157-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:31:17-49
158        </provider>
159
160        <service
160-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:39:9-45:35
161            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
161-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:40:13-88
162            android:directBootAware="false"
162-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:41:13-44
163            android:enabled="@bool/enable_system_alarm_service_default"
163-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:42:13-72
164            android:exported="false" />
164-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:43:13-37
165        <service
165-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:46:9-52:35
166            android:name="androidx.work.impl.background.systemjob.SystemJobService"
166-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:47:13-84
167            android:directBootAware="false"
167-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:48:13-44
168            android:enabled="@bool/enable_system_job_service_default"
168-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:49:13-70
169            android:exported="true"
169-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:50:13-36
170            android:permission="android.permission.BIND_JOB_SERVICE" />
170-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:51:13-69
171        <service
171-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:53:9-59:35
172            android:name="androidx.work.impl.foreground.SystemForegroundService"
172-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:54:13-81
173            android:directBootAware="false"
173-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:55:13-44
174            android:enabled="@bool/enable_system_foreground_service_default"
174-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:56:13-77
175            android:exported="false" />
175-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:57:13-37
176
177        <receiver
177-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:61:9-66:35
178            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
178-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:62:13-88
179            android:directBootAware="false"
179-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:63:13-44
180            android:enabled="true"
180-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:64:13-35
181            android:exported="false" />
181-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:65:13-37
182        <receiver
182-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:67:9-77:20
183            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
183-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:68:13-106
184            android:directBootAware="false"
184-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:69:13-44
185            android:enabled="false"
185-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:70:13-36
186            android:exported="false" >
186-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:71:13-37
187            <intent-filter>
187-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:73:13-76:29
188                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
188-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:74:17-87
188-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:74:25-84
189                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
189-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:75:17-90
189-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:75:25-87
190            </intent-filter>
191        </receiver>
192        <receiver
192-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:78:9-88:20
193            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
193-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:79:13-104
194            android:directBootAware="false"
194-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:80:13-44
195            android:enabled="false"
195-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:81:13-36
196            android:exported="false" >
196-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:82:13-37
197            <intent-filter>
197-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:84:13-87:29
198                <action android:name="android.intent.action.BATTERY_OKAY" />
198-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:85:17-77
198-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:85:25-74
199                <action android:name="android.intent.action.BATTERY_LOW" />
199-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:86:17-76
199-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:86:25-73
200            </intent-filter>
201        </receiver>
202        <receiver
202-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:89:9-99:20
203            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
203-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:90:13-104
204            android:directBootAware="false"
204-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:91:13-44
205            android:enabled="false"
205-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:92:13-36
206            android:exported="false" >
206-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:93:13-37
207            <intent-filter>
207-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:95:13-98:29
208                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
208-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:96:17-83
208-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:96:25-80
209                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
209-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:97:17-82
209-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:97:25-79
210            </intent-filter>
211        </receiver>
212        <receiver
212-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:100:9-109:20
213            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
213-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:101:13-103
214            android:directBootAware="false"
214-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:102:13-44
215            android:enabled="false"
215-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:103:13-36
216            android:exported="false" >
216-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:104:13-37
217            <intent-filter>
217-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:106:13-108:29
218                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
218-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:107:17-79
218-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:107:25-76
219            </intent-filter>
220        </receiver>
221        <receiver
221-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:110:9-121:20
222            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
222-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:111:13-88
223            android:directBootAware="false"
223-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:112:13-44
224            android:enabled="false"
224-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:113:13-36
225            android:exported="false" >
225-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:114:13-37
226            <intent-filter>
226-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:116:13-120:29
227                <action android:name="android.intent.action.BOOT_COMPLETED" />
227-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:105:17-79
227-->/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/AndroidManifest.xml:105:25-76
228                <action android:name="android.intent.action.TIME_SET" />
228-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:118:17-73
228-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:118:25-70
229                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
229-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:119:17-81
229-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:119:25-78
230            </intent-filter>
231        </receiver>
232        <receiver
232-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:122:9-131:20
233            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
233-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:123:13-99
234            android:directBootAware="false"
234-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:124:13-44
235            android:enabled="@bool/enable_system_alarm_service_default"
235-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:125:13-72
236            android:exported="false" >
236-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:126:13-37
237            <intent-filter>
237-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:128:13-130:29
238                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
238-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:129:17-98
238-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:129:25-95
239            </intent-filter>
240        </receiver>
241        <receiver
241-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:132:9-142:20
242            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
242-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:133:13-78
243            android:directBootAware="false"
243-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:134:13-44
244            android:enabled="true"
244-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:135:13-35
245            android:exported="true"
245-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:136:13-36
246            android:permission="android.permission.DUMP" >
246-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:137:13-57
247            <intent-filter>
247-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:139:13-141:29
248                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
248-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:140:17-88
248-->[androidx.work:work-runtime:2.9.0] /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/AndroidManifest.xml:140:25-85
249            </intent-filter>
250        </receiver>
251
252        <service
252-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/8d1765fc745ef3335caadcdc97a01918/transformed/room-runtime-2.6.1/AndroidManifest.xml:24:9-28:63
253            android:name="androidx.room.MultiInstanceInvalidationService"
253-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/8d1765fc745ef3335caadcdc97a01918/transformed/room-runtime-2.6.1/AndroidManifest.xml:25:13-74
254            android:directBootAware="true"
254-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/8d1765fc745ef3335caadcdc97a01918/transformed/room-runtime-2.6.1/AndroidManifest.xml:26:13-43
255            android:exported="false" />
255-->[androidx.room:room-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.10/transforms/8d1765fc745ef3335caadcdc97a01918/transformed/room-runtime-2.6.1/AndroidManifest.xml:27:13-37
256
257        <receiver
257-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:34:9-52:20
258            android:name="androidx.profileinstaller.ProfileInstallReceiver"
258-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:35:13-76
259            android:directBootAware="false"
259-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:36:13-44
260            android:enabled="true"
260-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:37:13-35
261            android:exported="true"
261-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:38:13-36
262            android:permission="android.permission.DUMP" >
262-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:39:13-57
263            <intent-filter>
263-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:40:13-42:29
264                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
264-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:41:17-91
264-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:41:25-88
265            </intent-filter>
266            <intent-filter>
266-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:43:13-45:29
267                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
267-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:44:17-85
267-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:44:25-82
268            </intent-filter>
269            <intent-filter>
269-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:46:13-48:29
270                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
270-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:47:17-88
270-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:47:25-85
271            </intent-filter>
272            <intent-filter>
272-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:49:13-51:29
273                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
273-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:50:17-95
273-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/AndroidManifest.xml:50:25-92
274            </intent-filter>
275        </receiver>
276    </application>
277
278</manifest>
