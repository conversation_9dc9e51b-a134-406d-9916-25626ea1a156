<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="org.linphone:linphone-sdk-android:5.2.7" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/assets"><file name="org.linphone.core/share/belr/grammars/identity_grammar" path="/Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/assets/org.linphone.core/share/belr/grammars/identity_grammar"/><file name="org.linphone.core/share/belr/grammars/sdp_grammar" path="/Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/assets/org.linphone.core/share/belr/grammars/sdp_grammar"/><file name="org.linphone.core/share/belr/grammars/vcard_grammar" path="/Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/assets/org.linphone.core/share/belr/grammars/vcard_grammar"/><file name="org.linphone.core/share/belr/grammars/cpim_grammar" path="/Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/assets/org.linphone.core/share/belr/grammars/cpim_grammar"/><file name="org.linphone.core/share/belr/grammars/ics_grammar" path="/Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/assets/org.linphone.core/share/belr/grammars/ics_grammar"/><file name="org.linphone.core/share/images/nowebcamcif.jpg" path="/Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/assets/org.linphone.core/share/images/nowebcamcif.jpg"/><file name="org.linphone.core/share/linphone/rootca.pem" path="/Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/assets/org.linphone.core/share/linphone/rootca.pem"/><file name="org.linphone.core/share/sounds/linphone/silence.mkv" path="/Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/assets/org.linphone.core/share/sounds/linphone/silence.mkv"/><file name="org.linphone.core/share/sounds/linphone/dont_wait_too_long.mkv" path="/Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/assets/org.linphone.core/share/sounds/linphone/dont_wait_too_long.mkv"/><file name="org.linphone.core/share/sounds/linphone/toy-mono.wav" path="/Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/assets/org.linphone.core/share/sounds/linphone/toy-mono.wav"/><file name="org.linphone.core/share/sounds/linphone/hello8000.wav" path="/Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/assets/org.linphone.core/share/sounds/linphone/hello8000.wav"/><file name="org.linphone.core/share/sounds/linphone/rings/four_hands_together.mkv" path="/Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/assets/org.linphone.core/share/sounds/linphone/rings/four_hands_together.mkv"/><file name="org.linphone.core/share/sounds/linphone/rings/notes_of_the_optimistic.mkv" path="/Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/assets/org.linphone.core/share/sounds/linphone/rings/notes_of_the_optimistic.mkv"/><file name="org.linphone.core/share/sounds/linphone/rings/oldphone-mono.wav" path="/Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/assets/org.linphone.core/share/sounds/linphone/rings/oldphone-mono.wav"/><file name="org.linphone.core/share/sounds/linphone/rings/house_keeping.mkv" path="/Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/assets/org.linphone.core/share/sounds/linphone/rings/house_keeping.mkv"/><file name="org.linphone.core/share/sounds/linphone/rings/leaving_dreams.mkv" path="/Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/assets/org.linphone.core/share/sounds/linphone/rings/leaving_dreams.mkv"/><file name="org.linphone.core/share/sounds/linphone/rings/soft_as_snow.mkv" path="/Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/assets/org.linphone.core/share/sounds/linphone/rings/soft_as_snow.mkv"/><file name="org.linphone.core/share/sounds/linphone/rings/its_a_game.mkv" path="/Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/assets/org.linphone.core/share/sounds/linphone/rings/its_a_game.mkv"/><file name="org.linphone.core/share/sounds/linphone/ringback.wav" path="/Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/assets/org.linphone.core/share/sounds/linphone/ringback.wav"/><file name="org.linphone.core/share/sounds/linphone/hello16000.wav" path="/Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/assets/org.linphone.core/share/sounds/linphone/hello16000.wav"/><file name="org.linphone.core/share/sounds/linphone/incoming_chat.wav" path="/Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/assets/org.linphone.core/share/sounds/linphone/incoming_chat.wav"/></source></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/debug/assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Downloads/project/html/VOIP/Android/app/build/intermediates/shader_assets/debug/out"/></dataSet></merger>