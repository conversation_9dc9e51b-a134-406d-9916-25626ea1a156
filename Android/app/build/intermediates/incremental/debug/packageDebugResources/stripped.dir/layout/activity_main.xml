<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.MainActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            app:title="@string/app_name" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Call Forwarding Status Card -->
            <com.google.android.material.card.MaterialCardView
                style="@style/CardStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/title_call_forwarding"
                        android:textAppearance="?attr/textAppearanceHeadline6"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <View
                            android:id="@+id/statusIndicator"
                            style="@style/StatusIndicator" />

                        <TextView
                            android:id="@+id/tvForwardingStatus"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/status_inactive"
                            android:textAppearance="?attr/textAppearanceBody1" />

                        <com.google.android.material.switchmaterial.SwitchMaterial
                            android:id="@+id/switchForwarding"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvStatusDetails"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="Call forwarding is currently disabled"
                        android:textAppearance="?attr/textAppearanceBody2"
                        android:textColor="?android:attr/textColorSecondary" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Current Call Information Card -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/cardCurrentCall"
                style="@style/CardStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Current Call"
                        android:textAppearance="?attr/textAppearanceHeadline6"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvCurrentCallNumber"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:textAppearance="?attr/textAppearanceBody1"
                        tools:text="+1234567890" />

                    <TextView
                        android:id="@+id/tvCurrentCallStatus"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:textAppearance="?attr/textAppearanceBody2"
                        android:textColor="?android:attr/textColorSecondary"
                        tools:text="Forwarding to destination..." />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Quick Actions -->
            <com.google.android.material.card.MaterialCardView
                style="@style/CardStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Quick Actions"
                        android:textAppearance="?attr/textAppearanceHeadline6"
                        android:textStyle="bold" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnConfiguration"
                        style="@style/SecondaryButton"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/title_configuration"
                        app:icon="@drawable/ic_settings" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnCallLogs"
                        style="@style/SecondaryButton"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/title_call_logs"
                        app:icon="@drawable/ic_history" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnSettings"
                        style="@style/SecondaryButton"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/title_settings"
                        app:icon="@drawable/ic_settings_app" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Statistics Card -->
            <com.google.android.material.card.MaterialCardView
                style="@style/CardStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Statistics"
                        android:textAppearance="?attr/textAppearanceHeadline6"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tvTotalForwarded"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0"
                                android:textAppearance="?attr/textAppearanceHeadline4"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Total Forwarded"
                                android:textAppearance="?attr/textAppearanceCaption"
                                android:textColor="?android:attr/textColorSecondary" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/tvTotalFailed"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0"
                                android:textAppearance="?attr/textAppearanceHeadline4"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Failed"
                                android:textAppearance="?attr/textAppearanceCaption"
                                android:textColor="?android:attr/textColorSecondary" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
