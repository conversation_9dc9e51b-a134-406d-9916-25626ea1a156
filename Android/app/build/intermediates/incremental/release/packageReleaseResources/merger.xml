<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/res"><file name="ic_phone_forwarded" path="/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/res/drawable/ic_phone_forwarded.xml" qualifiers="" type="drawable"/><file name="ic_history" path="/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/res/drawable/ic_history.xml" qualifiers="" type="drawable"/><file name="status_indicator" path="/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/res/drawable/status_indicator.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/res/drawable/ic_settings.xml" qualifiers="" type="drawable"/><file name="ic_settings_app" path="/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/res/drawable/ic_settings_app.xml" qualifiers="" type="drawable"/><file name="activity_main" path="/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/res/layout/activity_main.xml" qualifiers="" type="layout"/><file path="/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/res/values/colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="primary">#FF2196F3</color><color name="primary_dark">#FF1976D2</color><color name="accent">#FF4CAF50</color><color name="status_active">#FF4CAF50</color><color name="status_inactive">#FF9E9E9E</color><color name="status_error">#FFF44336</color><color name="status_connecting">#FFFF9800</color><color name="background_light">#FFFAFAFA</color><color name="background_dark">#FF121212</color><color name="surface_light">#FFFFFFFF</color><color name="surface_dark">#FF1E1E1E</color><color name="text_primary_light">#DE000000</color><color name="text_secondary_light">#99000000</color><color name="text_primary_dark">#FFFFFFFF</color><color name="text_secondary_dark">#B3FFFFFF</color></file><file path="/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/res/values/themes.xml" qualifiers=""><style name="Theme.VoIPCallForwarder" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/accent</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
        
    </style><style name="CardStyle" parent="Widget.Material3.CardView.Elevated">
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
        <item name="android:layout_margin">8dp</item>
    </style><style name="PrimaryButton" parent="Widget.Material3.Button">
        <item name="android:layout_height">56dp</item>
        <item name="android:layout_marginTop">16dp</item>
        <item name="android:layout_marginBottom">8dp</item>
        <item name="cornerRadius">8dp</item>
    </style><style name="SecondaryButton" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:layout_height">48dp</item>
        <item name="android:layout_marginTop">8dp</item>
        <item name="android:layout_marginBottom">8dp</item>
        <item name="cornerRadius">8dp</item>
    </style><style name="TextInputStyle" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="android:layout_marginTop">8dp</item>
        <item name="android:layout_marginBottom">8dp</item>
        <item name="boxCornerRadiusTopStart">8dp</item>
        <item name="boxCornerRadiusTopEnd">8dp</item>
        <item name="boxCornerRadiusBottomStart">8dp</item>
        <item name="boxCornerRadiusBottomEnd">8dp</item>
    </style><style name="StatusIndicator">
        <item name="android:layout_width">12dp</item>
        <item name="android:layout_height">12dp</item>
        <item name="android:layout_marginEnd">8dp</item>
        <item name="android:background">@drawable/status_indicator</item>
    </style></file><file path="/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/res/values/strings.xml" qualifiers=""><string name="app_name">VoIP Call Forwarder</string><string name="title_call_forwarding">Call Forwarding</string><string name="enable_forwarding">Enable Call Forwarding</string><string name="disable_forwarding">Disable Call Forwarding</string><string name="forwarding_status">Forwarding Status</string><string name="status_active">Active</string><string name="status_inactive">Inactive</string><string name="status_connecting">Connecting...</string><string name="status_error">Error</string><string name="title_configuration">VoIP Configuration</string><string name="sip_server_address">SIP Server Address</string><string name="sip_server_port">SIP Server Port</string><string name="sip_username">Username</string><string name="sip_password">Password</string><string name="sip_domain">Domain</string><string name="forwarding_destination">Forwarding Destination</string><string name="save_configuration">Save Configuration</string><string name="test_connection">Test Connection</string><string name="title_call_logs">Call Logs</string><string name="no_call_logs">No forwarded calls yet</string><string name="call_forwarded">Forwarded</string><string name="call_failed">Failed</string><string name="call_duration">Duration: %s</string><string name="clear_logs">Clear Logs</string><string name="title_settings">Settings</string><string name="auto_start_on_boot">Auto-start on boot</string><string name="show_call_notifications">Show call notifications</string><string name="log_forwarded_calls">Log forwarded calls</string><string name="permission_required">Permission Required</string><string name="phone_permission_rationale">This app needs phone permissions to detect incoming calls and forward them via VoIP.</string><string name="microphone_permission_rationale">This app needs microphone permission to handle audio during call forwarding.</string><string name="grant_permission">Grant Permission</string><string name="permission_denied">Permission denied. The app cannot function without required permissions.</string><string name="notification_forwarding_active">Call forwarding is active</string><string name="notification_incoming_call">Incoming call from %s</string><string name="notification_call_forwarded">Call forwarded to %s</string><string name="error_invalid_configuration">Invalid VoIP configuration</string><string name="error_connection_failed">Failed to connect to VoIP server</string><string name="error_forwarding_failed">Call forwarding failed</string><string name="error_no_forwarding_destination">No forwarding destination configured</string><string name="ok">OK</string><string name="cancel">Cancel</string><string name="retry">Retry</string><string name="settings">Settings</string><string name="hint_sip_server">e.g., sip.yourserver.com</string><string name="hint_sip_port">e.g., 5060</string><string name="hint_username">Your SIP username</string><string name="hint_password">Your SIP password</string><string name="hint_domain">e.g., yourserver.com</string><string name="hint_destination">e.g., <EMAIL></string></file><file name="backup_rules" path="/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/res/xml/backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/res/xml/data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/release/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Downloads/project/html/VOIP/Android/app/src/release/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Downloads/project/html/VOIP/Android/app/build/generated/res/resValues/release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Downloads/project/html/VOIP/Android/app/build/generated/res/resValues/release"/></dataSet><mergedItems/></merger>