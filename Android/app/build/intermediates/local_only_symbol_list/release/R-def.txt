R_DEF: Internal format may change without notice
local
color accent
color background_dark
color background_light
color black
color primary
color primary_dark
color purple_200
color purple_500
color purple_700
color status_active
color status_connecting
color status_error
color status_inactive
color surface_dark
color surface_light
color teal_200
color teal_700
color text_primary_dark
color text_primary_light
color text_secondary_dark
color text_secondary_light
color white
drawable ic_history
drawable ic_phone_forwarded
drawable ic_settings
drawable ic_settings_app
drawable status_indicator
id btnCallLogs
id btnConfiguration
id btnSettings
id cardCurrentCall
id statusIndicator
id switchForwarding
id toolbar
id tvCurrentCallNumber
id tvCurrentCallStatus
id tvForwardingStatus
id tvStatusDetails
id tvTotalFailed
id tvTotalForwarded
layout activity_main
string app_name
string auto_start_on_boot
string call_duration
string call_failed
string call_forwarded
string cancel
string clear_logs
string disable_forwarding
string enable_forwarding
string error_connection_failed
string error_forwarding_failed
string error_invalid_configuration
string error_no_forwarding_destination
string forwarding_destination
string forwarding_status
string grant_permission
string hint_destination
string hint_domain
string hint_password
string hint_sip_port
string hint_sip_server
string hint_username
string log_forwarded_calls
string microphone_permission_rationale
string no_call_logs
string notification_call_forwarded
string notification_forwarding_active
string notification_incoming_call
string ok
string permission_denied
string permission_required
string phone_permission_rationale
string retry
string save_configuration
string settings
string show_call_notifications
string sip_domain
string sip_password
string sip_server_address
string sip_server_port
string sip_username
string status_active
string status_connecting
string status_error
string status_inactive
string test_connection
string title_call_forwarding
string title_call_logs
string title_configuration
string title_settings
style CardStyle
style PrimaryButton
style SecondaryButton
style StatusIndicator
style TextInputStyle
style Theme.VoIPCallForwarder
xml backup_rules
xml data_extraction_rules
