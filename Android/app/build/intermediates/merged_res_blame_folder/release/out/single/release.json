[{"merged": "com.voipforwarder.app-merged_res-48:/xml_backup_rules.xml.flat", "source": "com.voipforwarder.app-main-49:/xml/backup_rules.xml"}, {"merged": "com.voipforwarder.app-merged_res-48:/drawable_ic_settings.xml.flat", "source": "com.voipforwarder.app-main-49:/drawable/ic_settings.xml"}, {"merged": "com.voipforwarder.app-merged_res-48:/drawable_status_indicator.xml.flat", "source": "com.voipforwarder.app-main-49:/drawable/status_indicator.xml"}, {"merged": "com.voipforwarder.app-merged_res-48:/layout_activity_main.xml.flat", "source": "com.voipforwarder.app-main-49:/layout/activity_main.xml"}, {"merged": "com.voipforwarder.app-merged_res-48:/drawable_ic_phone_forwarded.xml.flat", "source": "com.voipforwarder.app-main-49:/drawable/ic_phone_forwarded.xml"}, {"merged": "com.voipforwarder.app-merged_res-48:/drawable_ic_settings_app.xml.flat", "source": "com.voipforwarder.app-main-49:/drawable/ic_settings_app.xml"}, {"merged": "com.voipforwarder.app-merged_res-48:/drawable_ic_history.xml.flat", "source": "com.voipforwarder.app-main-49:/drawable/ic_history.xml"}, {"merged": "com.voipforwarder.app-merged_res-48:/xml_data_extraction_rules.xml.flat", "source": "com.voipforwarder.app-main-49:/xml/data_extraction_rules.xml"}]