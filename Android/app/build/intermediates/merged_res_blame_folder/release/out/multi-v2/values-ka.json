{"logs": [{"outputFile": "com.voipforwarder.app-mergeReleaseResources-46:/values-ka/values-ka.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10/transforms/6acec092cc653e34676ce455eae5b61f/transformed/material-1.10.0/res/values-ka/values-ka.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,351,425,509,602,696,795,920,1008,1075,1172,1241,1304,1391,1455,1521,1581,1650,1711,1765,1880,1939,1999,2053,2125,2255,2343,2427,2565,2643,2719,2858,2952,3032,3088,3142,3208,3281,3359,3445,3529,3602,3680,3758,3833,3943,4033,4108,4202,4300,4374,4451,4551,4604,4688,4756,4845,4934,4996,5061,5124,5194,5301,5401,5501,5597,5657,5715", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,77,73,83,92,93,98,124,87,66,96,68,62,86,63,65,59,68,60,53,114,58,59,53,71,129,87,83,137,77,75,138,93,79,55,53,65,72,77,85,83,72,77,77,74,109,89,74,93,97,73,76,99,52,83,67,88,88,61,64,62,69,106,99,99,95,59,57,79", "endOffsets": "268,346,420,504,597,691,790,915,1003,1070,1167,1236,1299,1386,1450,1516,1576,1645,1706,1760,1875,1934,1994,2048,2120,2250,2338,2422,2560,2638,2714,2853,2947,3027,3083,3137,3203,3276,3354,3440,3524,3597,3675,3753,3828,3938,4028,4103,4197,4295,4369,4446,4546,4599,4683,4751,4840,4929,4991,5056,5119,5189,5296,5396,5496,5592,5652,5710,5790"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3026,3104,3178,3262,3355,4173,4272,4397,4485,4552,4649,4718,4781,4868,4932,4998,5058,5127,5188,5242,5357,5416,5476,5530,5602,5732,5820,5904,6042,6120,6196,6335,6429,6509,6565,6619,6685,6758,6836,6922,7006,7079,7157,7235,7310,7420,7510,7585,7679,7777,7851,7928,8028,8081,8165,8233,8322,8411,8473,8538,8601,8671,8778,8878,8978,9074,9134,9192", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "12,77,73,83,92,93,98,124,87,66,96,68,62,86,63,65,59,68,60,53,114,58,59,53,71,129,87,83,137,77,75,138,93,79,55,53,65,72,77,85,83,72,77,77,74,109,89,74,93,97,73,76,99,52,83,67,88,88,61,64,62,69,106,99,99,95,59,57,79", "endOffsets": "318,3099,3173,3257,3350,3444,4267,4392,4480,4547,4644,4713,4776,4863,4927,4993,5053,5122,5183,5237,5352,5411,5471,5525,5597,5727,5815,5899,6037,6115,6191,6330,6424,6504,6560,6614,6680,6753,6831,6917,7001,7074,7152,7230,7305,7415,7505,7580,7674,7772,7846,7923,8023,8076,8160,8228,8317,8406,8468,8533,8596,8666,8773,8873,8973,9069,9129,9187,9267"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/2e37c0ed8bc86c66b0dc0e6a1f68ae10/transformed/core-1.12.0/res/values-ka/values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,557,661,779", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "146,248,347,446,552,656,774,875"}, "to": {"startLines": "38,39,40,41,42,43,44,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3449,3545,3647,3746,3845,3951,4055,9354", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "3540,3642,3741,3840,3946,4050,4168,9450"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/3580a7a86416f0c987c68a95cd38672e/transformed/appcompat-1.6.1/res/values-ka/values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,316,427,513,618,731,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1912,2025,2131,2229,2342,2447,2551,2709,2808", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "208,311,422,508,613,726,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1907,2020,2126,2224,2337,2442,2546,2704,2803,2885"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,534,645,731,836,949,1032,1111,1202,1295,1390,1484,1584,1677,1772,1867,1958,2049,2130,2243,2349,2447,2560,2665,2769,2927,9272", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "426,529,640,726,831,944,1027,1106,1197,1290,1385,1479,1579,1672,1767,1862,1953,2044,2125,2238,2344,2442,2555,2660,2764,2922,3021,9349"}}]}]}