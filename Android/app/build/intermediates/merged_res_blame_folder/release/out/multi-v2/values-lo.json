{"logs": [{"outputFile": "com.voipforwarder.app-mergeReleaseResources-46:/values-lo/values-lo.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10/transforms/2e37c0ed8bc86c66b0dc0e6a1f68ae10/transformed/core-1.12.0/res/values-lo/values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,552,650,761", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "146,249,348,446,547,645,756,857"}, "to": {"startLines": "38,39,40,41,42,43,44,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3381,3477,3580,3679,3777,3878,3976,9133", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "3472,3575,3674,3772,3873,3971,4082,9229"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/6acec092cc653e34676ce455eae5b61f/transformed/material-1.10.0/res/values-lo/values-lo.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,343,414,495,581,664,779,898,981,1047,1136,1205,1264,1359,1425,1490,1548,1613,1674,1734,1840,1901,1961,2019,2090,2209,2295,2377,2520,2595,2671,2802,2892,2970,3025,3080,3146,3215,3289,3368,3447,3520,3597,3666,3736,3833,3918,3993,4086,4179,4253,4322,4416,4468,4551,4618,4702,4786,4848,4912,4975,5045,5144,5242,5337,5431,5490,5549", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,73,70,80,85,82,114,118,82,65,88,68,58,94,65,64,57,64,60,59,105,60,59,57,70,118,85,81,142,74,75,130,89,77,54,54,65,68,73,78,78,72,76,68,69,96,84,74,92,92,73,68,93,51,82,66,83,83,61,63,62,69,98,97,94,93,58,58,78", "endOffsets": "264,338,409,490,576,659,774,893,976,1042,1131,1200,1259,1354,1420,1485,1543,1608,1669,1729,1835,1896,1956,2014,2085,2204,2290,2372,2515,2590,2666,2797,2887,2965,3020,3075,3141,3210,3284,3363,3442,3515,3592,3661,3731,3828,3913,3988,4081,4174,4248,4317,4411,4463,4546,4613,4697,4781,4843,4907,4970,5040,5139,5237,5332,5426,5485,5544,5623"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2986,3060,3131,3212,3298,4087,4202,4321,4404,4470,4559,4628,4687,4782,4848,4913,4971,5036,5097,5157,5263,5324,5384,5442,5513,5632,5718,5800,5943,6018,6094,6225,6315,6393,6448,6503,6569,6638,6712,6791,6870,6943,7020,7089,7159,7256,7341,7416,7509,7602,7676,7745,7839,7891,7974,8041,8125,8209,8271,8335,8398,8468,8567,8665,8760,8854,8913,8972", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "12,73,70,80,85,82,114,118,82,65,88,68,58,94,65,64,57,64,60,59,105,60,59,57,70,118,85,81,142,74,75,130,89,77,54,54,65,68,73,78,78,72,76,68,69,96,84,74,92,92,73,68,93,51,82,66,83,83,61,63,62,69,98,97,94,93,58,58,78", "endOffsets": "314,3055,3126,3207,3293,3376,4197,4316,4399,4465,4554,4623,4682,4777,4843,4908,4966,5031,5092,5152,5258,5319,5379,5437,5508,5627,5713,5795,5938,6013,6089,6220,6310,6388,6443,6498,6564,6633,6707,6786,6865,6938,7015,7084,7154,7251,7336,7411,7504,7597,7671,7740,7834,7886,7969,8036,8120,8204,8266,8330,8393,8463,8562,8660,8755,8849,8908,8967,9046"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/3580a7a86416f0c987c68a95cd38672e/transformed/appcompat-1.6.1/res/values-lo/values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,424,509,613,724,802,879,970,1063,1155,1249,1349,1442,1537,1633,1724,1815,1896,2003,2107,2205,2308,2412,2516,2673,2772", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "203,306,419,504,608,719,797,874,965,1058,1150,1244,1344,1437,1532,1628,1719,1810,1891,1998,2102,2200,2303,2407,2511,2668,2767,2849"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,422,525,638,723,827,938,1016,1093,1184,1277,1369,1463,1563,1656,1751,1847,1938,2029,2110,2217,2321,2419,2522,2626,2730,2887,9051", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "417,520,633,718,822,933,1011,1088,1179,1272,1364,1458,1558,1651,1746,1842,1933,2024,2105,2212,2316,2414,2517,2621,2725,2882,2981,9128"}}]}]}