{"logs": [{"outputFile": "com.voipforwarder.app-mergeReleaseResources-46:/values-ta/values-ta.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10/transforms/6acec092cc653e34676ce455eae5b61f/transformed/material-1.10.0/res/values-ta/values-ta.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,360,446,530,633,727,836,954,1038,1102,1210,1278,1339,1447,1514,1600,1658,1742,1809,1863,1986,2048,2111,2165,2253,2381,2467,2549,2681,2761,2842,2998,3087,3171,3228,3280,3346,3431,3519,3611,3691,3760,3837,3917,3985,4100,4199,4282,4374,4468,4542,4628,4722,4772,4855,4921,5006,5093,5156,5221,5284,5353,5461,5559,5657,5754,5815,5871", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,87,85,83,102,93,108,117,83,63,107,67,60,107,66,85,57,83,66,53,122,61,62,53,87,127,85,81,131,79,80,155,88,83,56,51,65,84,87,91,79,68,76,79,67,114,98,82,91,93,73,85,93,49,82,65,84,86,62,64,62,68,107,97,97,96,60,55,85", "endOffsets": "267,355,441,525,628,722,831,949,1033,1097,1205,1273,1334,1442,1509,1595,1653,1737,1804,1858,1981,2043,2106,2160,2248,2376,2462,2544,2676,2756,2837,2993,3082,3166,3223,3275,3341,3426,3514,3606,3686,3755,3832,3912,3980,4095,4194,4277,4369,4463,4537,4623,4717,4767,4850,4916,5001,5088,5151,5216,5279,5348,5456,5554,5652,5749,5810,5866,5952"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3088,3176,3262,3346,3449,4289,4398,4516,4600,4664,4772,4840,4901,5009,5076,5162,5220,5304,5371,5425,5548,5610,5673,5727,5815,5943,6029,6111,6243,6323,6404,6560,6649,6733,6790,6842,6908,6993,7081,7173,7253,7322,7399,7479,7547,7662,7761,7844,7936,8030,8104,8190,8284,8334,8417,8483,8568,8655,8718,8783,8846,8915,9023,9121,9219,9316,9377,9433", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "12,87,85,83,102,93,108,117,83,63,107,67,60,107,66,85,57,83,66,53,122,61,62,53,87,127,85,81,131,79,80,155,88,83,56,51,65,84,87,91,79,68,76,79,67,114,98,82,91,93,73,85,93,49,82,65,84,86,62,64,62,68,107,97,97,96,60,55,85", "endOffsets": "317,3171,3257,3341,3444,3538,4393,4511,4595,4659,4767,4835,4896,5004,5071,5157,5215,5299,5366,5420,5543,5605,5668,5722,5810,5938,6024,6106,6238,6318,6399,6555,6644,6728,6785,6837,6903,6988,7076,7168,7248,7317,7394,7474,7542,7657,7756,7839,7931,8025,8099,8185,8279,8329,8412,8478,8563,8650,8713,8778,8841,8910,9018,9116,9214,9311,9372,9428,9514"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/3580a7a86416f0c987c68a95cd38672e/transformed/appcompat-1.6.1/res/values-ta/values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,320,435,524,635,756,835,911,1009,1109,1204,1298,1405,1505,1607,1701,1799,1897,1978,2086,2189,2288,2404,2507,2612,2769,2871", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "213,315,430,519,630,751,830,906,1004,1104,1199,1293,1400,1500,1602,1696,1794,1892,1973,2081,2184,2283,2399,2502,2607,2764,2866,2948"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,435,537,652,741,852,973,1052,1128,1226,1326,1421,1515,1622,1722,1824,1918,2016,2114,2195,2303,2406,2505,2621,2724,2829,2986,9519", "endColumns": "112,101,114,88,110,120,78,75,97,99,94,93,106,99,101,93,97,97,80,107,102,98,115,102,104,156,101,81", "endOffsets": "430,532,647,736,847,968,1047,1123,1221,1321,1416,1510,1617,1717,1819,1913,2011,2109,2190,2298,2401,2500,2616,2719,2824,2981,3083,9596"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/2e37c0ed8bc86c66b0dc0e6a1f68ae10/transformed/core-1.12.0/res/values-ta/values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,558,673,801", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "146,249,348,446,553,668,796,897"}, "to": {"startLines": "38,39,40,41,42,43,44,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3543,3639,3742,3841,3939,4046,4161,9601", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "3634,3737,3836,3934,4041,4156,4284,9697"}}]}]}