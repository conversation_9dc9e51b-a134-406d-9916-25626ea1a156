{"logs": [{"outputFile": "com.voipforwarder.app-mergeDebugResources-46:/values-sr/values-sr.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10/transforms/2e37c0ed8bc86c66b0dc0e6a1f68ae10/transformed/core-1.12.0/res/values-sr/values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "39,40,41,42,43,44,45,110", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3531,3629,3731,3828,3932,4036,4141,9411", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "3624,3726,3823,3927,4031,4136,4252,9507"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/3580a7a86416f0c987c68a95cd38672e/transformed/appcompat-1.6.1/res/values-sr/values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,896,987,1080,1175,1269,1369,1462,1557,1662,1753,1844,1930,2035,2141,2244,2350,2459,2566,2736,2833", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,810,891,982,1075,1170,1264,1364,1457,1552,1657,1748,1839,1925,2030,2136,2239,2345,2454,2561,2731,2828,2915"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "371,478,579,685,771,875,997,1081,1162,1253,1346,1441,1535,1635,1728,1823,1928,2019,2110,2196,2301,2407,2510,2616,2725,2832,3002,9324", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "473,574,680,766,870,992,1076,1157,1248,1341,1436,1530,1630,1723,1818,1923,2014,2105,2191,2296,2402,2505,2611,2720,2827,2997,3094,9406"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/6acec092cc653e34676ce455eae5b61f/transformed/material-1.10.0/res/values-sr/values-sr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,321,397,473,553,660,753,847,978,1059,1125,1217,1285,1348,1451,1511,1577,1633,1704,1764,1818,1930,1987,2048,2102,2178,2303,2389,2472,2610,2691,2774,2905,2993,3071,3125,3181,3247,3321,3399,3488,3570,3645,3721,3796,3867,3974,4064,4137,4229,4325,4397,4473,4569,4622,4704,4771,4858,4945,5007,5071,5134,5203,5308,5418,5514,5622,5680,5740", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "12,75,75,79,106,92,93,130,80,65,91,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,85,82,137,80,82,130,87,77,53,55,65,73,77,88,81,74,75,74,70,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79", "endOffsets": "316,392,468,548,655,748,842,973,1054,1120,1212,1280,1343,1446,1506,1572,1628,1699,1759,1813,1925,1982,2043,2097,2173,2298,2384,2467,2605,2686,2769,2900,2988,3066,3120,3176,3242,3316,3394,3483,3565,3640,3716,3791,3862,3969,4059,4132,4224,4320,4392,4468,4564,4617,4699,4766,4853,4940,5002,5066,5129,5198,5303,5413,5509,5617,5675,5735,5815"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3099,3175,3251,3331,3438,4257,4351,4482,4563,4629,4721,4789,4852,4955,5015,5081,5137,5208,5268,5322,5434,5491,5552,5606,5682,5807,5893,5976,6114,6195,6278,6409,6497,6575,6629,6685,6751,6825,6903,6992,7074,7149,7225,7300,7371,7478,7568,7641,7733,7829,7901,7977,8073,8126,8208,8275,8362,8449,8511,8575,8638,8707,8812,8922,9018,9126,9184,9244", "endLines": "6,34,35,36,37,38,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108", "endColumns": "12,75,75,79,106,92,93,130,80,65,91,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,85,82,137,80,82,130,87,77,53,55,65,73,77,88,81,74,75,74,70,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79", "endOffsets": "366,3170,3246,3326,3433,3526,4346,4477,4558,4624,4716,4784,4847,4950,5010,5076,5132,5203,5263,5317,5429,5486,5547,5601,5677,5802,5888,5971,6109,6190,6273,6404,6492,6570,6624,6680,6746,6820,6898,6987,7069,7144,7220,7295,7366,7473,7563,7636,7728,7824,7896,7972,8068,8121,8203,8270,8357,8444,8506,8570,8633,8702,8807,8917,9013,9121,9179,9239,9319"}}]}]}