{"logs": [{"outputFile": "com.voipforwarder.app-mergeDebugResources-46:/values-bs/values-bs.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.10/transforms/3580a7a86416f0c987c68a95cd38672e/transformed/appcompat-1.6.1/res/values-bs/values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,323,430,516,620,742,827,909,1000,1093,1188,1282,1382,1475,1570,1665,1756,1847,1935,2038,2142,2243,2348,2462,2565,2734,2830", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,100,104,113,102,168,95,86", "endOffsets": "221,318,425,511,615,737,822,904,995,1088,1183,1277,1377,1470,1565,1660,1751,1842,1930,2033,2137,2238,2343,2457,2560,2729,2825,2912"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "377,498,595,702,788,892,1014,1099,1181,1272,1365,1460,1554,1654,1747,1842,1937,2028,2119,2207,2310,2414,2515,2620,2734,2837,3006,9359", "endColumns": "120,96,106,85,103,121,84,81,90,92,94,93,99,92,94,94,90,90,87,102,103,100,104,113,102,168,95,86", "endOffsets": "493,590,697,783,887,1009,1094,1176,1267,1360,1455,1549,1649,1742,1837,1932,2023,2114,2202,2305,2409,2510,2615,2729,2832,3001,3097,9441"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/2e37c0ed8bc86c66b0dc0e6a1f68ae10/transformed/core-1.12.0/res/values-bs/values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,457,561,663,780", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "148,250,348,452,556,658,775,876"}, "to": {"startLines": "39,40,41,42,43,44,45,110", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3539,3637,3739,3837,3941,4045,4147,9446", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "3632,3734,3832,3936,4040,4142,4259,9542"}}, {"source": "/Users/<USER>/.gradle/caches/8.10/transforms/6acec092cc653e34676ce455eae5b61f/transformed/material-1.10.0/res/values-bs/values-bs.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,327,406,486,568,670,764,860,986,1067,1133,1225,1302,1365,1473,1533,1599,1655,1726,1786,1840,1959,2016,2078,2132,2207,2331,2419,2502,2647,2732,2818,2951,3039,3117,3171,3225,3291,3365,3443,3530,3612,3684,3761,3834,3904,4013,4106,4178,4270,4366,4440,4516,4612,4665,4747,4814,4901,4988,5050,5114,5177,5246,5354,5459,5560,5663,5721,5779", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "12,78,79,81,101,93,95,125,80,65,91,76,62,107,59,65,55,70,59,53,118,56,61,53,74,123,87,82,144,84,85,132,87,77,53,53,65,73,77,86,81,71,76,72,69,108,92,71,91,95,73,75,95,52,81,66,86,86,61,63,62,68,107,104,100,102,57,57,79", "endOffsets": "322,401,481,563,665,759,855,981,1062,1128,1220,1297,1360,1468,1528,1594,1650,1721,1781,1835,1954,2011,2073,2127,2202,2326,2414,2497,2642,2727,2813,2946,3034,3112,3166,3220,3286,3360,3438,3525,3607,3679,3756,3829,3899,4008,4101,4173,4265,4361,4435,4511,4607,4660,4742,4809,4896,4983,5045,5109,5172,5241,5349,5454,5555,5658,5716,5774,5854"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3102,3181,3261,3343,3445,4264,4360,4486,4567,4633,4725,4802,4865,4973,5033,5099,5155,5226,5286,5340,5459,5516,5578,5632,5707,5831,5919,6002,6147,6232,6318,6451,6539,6617,6671,6725,6791,6865,6943,7030,7112,7184,7261,7334,7404,7513,7606,7678,7770,7866,7940,8016,8112,8165,8247,8314,8401,8488,8550,8614,8677,8746,8854,8959,9060,9163,9221,9279", "endLines": "6,34,35,36,37,38,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108", "endColumns": "12,78,79,81,101,93,95,125,80,65,91,76,62,107,59,65,55,70,59,53,118,56,61,53,74,123,87,82,144,84,85,132,87,77,53,53,65,73,77,86,81,71,76,72,69,108,92,71,91,95,73,75,95,52,81,66,86,86,61,63,62,68,107,104,100,102,57,57,79", "endOffsets": "372,3176,3256,3338,3440,3534,4355,4481,4562,4628,4720,4797,4860,4968,5028,5094,5150,5221,5281,5335,5454,5511,5573,5627,5702,5826,5914,5997,6142,6227,6313,6446,6534,6612,6666,6720,6786,6860,6938,7025,7107,7179,7256,7329,7399,7508,7601,7673,7765,7861,7935,8011,8107,8160,8242,8309,8396,8483,8545,8609,8672,8741,8849,8954,9055,9158,9216,9274,9354"}}]}]}