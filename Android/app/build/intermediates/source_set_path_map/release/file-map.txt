com.voipforwarder.app-jetified-fragment-ktx-1.6.2-0 /Users/<USER>/.gradle/caches/8.10/transforms/0b2f5cfd2fa7c930f1c14f86229d1b82/transformed/jetified-fragment-ktx-1.6.2/res
com.voipforwarder.app-jetified-viewpager2-1.0.0-1 /Users/<USER>/.gradle/caches/8.10/transforms/0c24fbcac79485ba2ba041aceec7fe06/transformed/jetified-viewpager2-1.0.0/res
com.voipforwarder.app-transition-1.2.0-2 /Users/<USER>/.gradle/caches/8.10/transforms/109750b43eadf1884097820777268d11/transformed/transition-1.2.0/res
com.voipforwarder.app-constraintlayout-2.1.4-3 /Users/<USER>/.gradle/caches/8.10/transforms/18635bf40db815413205a38e8a73f34f/transformed/constraintlayout-2.1.4/res
com.voipforwarder.app-jetified-savedstate-ktx-1.2.1-4 /Users/<USER>/.gradle/caches/8.10/transforms/1dc42bb9c5b0070ee72e9009dbaf4cd3/transformed/jetified-savedstate-ktx-1.2.1/res
com.voipforwarder.app-jetified-lifecycle-viewmodel-savedstate-2.7.0-5 /Users/<USER>/.gradle/caches/8.10/transforms/239f3631f2f97280cf20bf62ea9a2d0a/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/res
com.voipforwarder.app-jetified-emoji2-views-helper-1.2.0-6 /Users/<USER>/.gradle/caches/8.10/transforms/2bf6ec78eb12d6e4d1c4fe660a5f72a6/transformed/jetified-emoji2-views-helper-1.2.0/res
com.voipforwarder.app-core-1.12.0-7 /Users/<USER>/.gradle/caches/8.10/transforms/2e37c0ed8bc86c66b0dc0e6a1f68ae10/transformed/core-1.12.0/res
com.voipforwarder.app-databinding-adapters-8.1.2-8 /Users/<USER>/.gradle/caches/8.10/transforms/315b8622b523bc40779937f1c745c614/transformed/databinding-adapters-8.1.2/res
com.voipforwarder.app-appcompat-1.6.1-9 /Users/<USER>/.gradle/caches/8.10/transforms/3580a7a86416f0c987c68a95cd38672e/transformed/appcompat-1.6.1/res
com.voipforwarder.app-work-runtime-2.9.0-10 /Users/<USER>/.gradle/caches/8.10/transforms/418346871e257bba62ed3a9bcd442a3c/transformed/work-runtime-2.9.0/res
com.voipforwarder.app-sqlite-2.4.0-11 /Users/<USER>/.gradle/caches/8.10/transforms/42896e24c29f36fd51c05c60647a5de5/transformed/sqlite-2.4.0/res
com.voipforwarder.app-recyclerview-1.1.0-12 /Users/<USER>/.gradle/caches/8.10/transforms/44a1313f6a65366fd9f905171e2332a1/transformed/recyclerview-1.1.0/res
com.voipforwarder.app-jetified-savedstate-1.2.1-13 /Users/<USER>/.gradle/caches/8.10/transforms/63403f23b76b93551d7a499f12ad436f/transformed/jetified-savedstate-1.2.1/res
com.voipforwarder.app-jetified-lifecycle-service-2.7.0-14 /Users/<USER>/.gradle/caches/8.10/transforms/677e779bf35e6ed8651937a72b645a55/transformed/jetified-lifecycle-service-2.7.0/res
com.voipforwarder.app-work-runtime-ktx-2.9.0-15 /Users/<USER>/.gradle/caches/8.10/transforms/6a345963804fd3e24cc26789278beadc/transformed/work-runtime-ktx-2.9.0/res
com.voipforwarder.app-material-1.10.0-16 /Users/<USER>/.gradle/caches/8.10/transforms/6acec092cc653e34676ce455eae5b61f/transformed/material-1.10.0/res
com.voipforwarder.app-jetified-lifecycle-process-2.7.0-17 /Users/<USER>/.gradle/caches/8.10/transforms/6bad45c3e73cba33f913594ccdebc03b/transformed/jetified-lifecycle-process-2.7.0/res
com.voipforwarder.app-sqlite-framework-2.4.0-18 /Users/<USER>/.gradle/caches/8.10/transforms/723894d40f040f2d99322a3b4d924481/transformed/sqlite-framework-2.4.0/res
com.voipforwarder.app-jetified-lifecycle-runtime-ktx-2.7.0-19 /Users/<USER>/.gradle/caches/8.10/transforms/799b3444209b39e0ee5a8c274cc0fb85/transformed/jetified-lifecycle-runtime-ktx-2.7.0/res
com.voipforwarder.app-lifecycle-livedata-core-2.7.0-20 /Users/<USER>/.gradle/caches/8.10/transforms/8221539abda50c7aedc05e473fd47ad2/transformed/lifecycle-livedata-core-2.7.0/res
com.voipforwarder.app-jetified-room-ktx-2.6.1-21 /Users/<USER>/.gradle/caches/8.10/transforms/8a67b237605bc78a58fd00d9839fea0f/transformed/jetified-room-ktx-2.6.1/res
com.voipforwarder.app-room-runtime-2.6.1-22 /Users/<USER>/.gradle/caches/8.10/transforms/8d1765fc745ef3335caadcdc97a01918/transformed/room-runtime-2.6.1/res
com.voipforwarder.app-jetified-activity-1.8.2-23 /Users/<USER>/.gradle/caches/8.10/transforms/934d032ef5b3b32baa2f263d4a7385d5/transformed/jetified-activity-1.8.2/res
com.voipforwarder.app-lifecycle-livedata-2.7.0-24 /Users/<USER>/.gradle/caches/8.10/transforms/97e9b545d237febaa8a0dbb7e35b7bd3/transformed/lifecycle-livedata-2.7.0/res
com.voipforwarder.app-coordinatorlayout-1.1.0-25 /Users/<USER>/.gradle/caches/8.10/transforms/a0b59593001da7f2cbb8314c1e949cdd/transformed/coordinatorlayout-1.1.0/res
com.voipforwarder.app-jetified-emoji2-1.2.0-26 /Users/<USER>/.gradle/caches/8.10/transforms/a30e1fe6c4ed5ace2aadb0cb60127282/transformed/jetified-emoji2-1.2.0/res
com.voipforwarder.app-jetified-appcompat-resources-1.6.1-27 /Users/<USER>/.gradle/caches/8.10/transforms/a6c35266896f2ea0a05f7fdc28d34949/transformed/jetified-appcompat-resources-1.6.1/res
com.voipforwarder.app-jetified-annotation-experimental-1.3.0-28 /Users/<USER>/.gradle/caches/8.10/transforms/a80e1e66913b6222963e0fd0b7173b24/transformed/jetified-annotation-experimental-1.3.0/res
com.voipforwarder.app-databinding-runtime-8.1.2-29 /Users/<USER>/.gradle/caches/8.10/transforms/b23a16b7b0131a48fd7ffadf311ccc9b/transformed/databinding-runtime-8.1.2/res
com.voipforwarder.app-core-runtime-2.2.0-30 /Users/<USER>/.gradle/caches/8.10/transforms/b256fdd9a0f4d0e56cb7b72d09215aa8/transformed/core-runtime-2.2.0/res
com.voipforwarder.app-jetified-activity-ktx-1.8.2-31 /Users/<USER>/.gradle/caches/8.10/transforms/b857dba085e192669fc59695bc457ea9/transformed/jetified-activity-ktx-1.8.2/res
com.voipforwarder.app-jetified-core-ktx-1.12.0-32 /Users/<USER>/.gradle/caches/8.10/transforms/b9b237c65d148162dba5f518ac7d9336/transformed/jetified-core-ktx-1.12.0/res
com.voipforwarder.app-jetified-profileinstaller-1.3.0-33 /Users/<USER>/.gradle/caches/8.10/transforms/c64506ac2c129f2f1eec1177c0f78691/transformed/jetified-profileinstaller-1.3.0/res
com.voipforwarder.app-jetified-lifecycle-livedata-ktx-2.7.0-34 /Users/<USER>/.gradle/caches/8.10/transforms/c876107dc7f1e228a2885a84a350be88/transformed/jetified-lifecycle-livedata-ktx-2.7.0/res
com.voipforwarder.app-jetified-lifecycle-viewmodel-ktx-2.7.0-35 /Users/<USER>/.gradle/caches/8.10/transforms/e275caffd8e058d114a615caf06cf4d7/transformed/jetified-lifecycle-viewmodel-ktx-2.7.0/res
com.voipforwarder.app-lifecycle-viewmodel-2.7.0-36 /Users/<USER>/.gradle/caches/8.10/transforms/e40b5ad95789bfc1e70901a054018305/transformed/lifecycle-viewmodel-2.7.0/res
com.voipforwarder.app-jetified-lifecycle-livedata-core-ktx-2.7.0-37 /Users/<USER>/.gradle/caches/8.10/transforms/e40ebc3ede25fe10436e2b9a67790948/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/res
com.voipforwarder.app-cardview-1.0.0-38 /Users/<USER>/.gradle/caches/8.10/transforms/e76478bf70112012d4563170db171db4/transformed/cardview-1.0.0/res
com.voipforwarder.app-fragment-1.6.2-39 /Users/<USER>/.gradle/caches/8.10/transforms/ec19ebc7e003b80add4308de17dd1c58/transformed/fragment-1.6.2/res
com.voipforwarder.app-lifecycle-runtime-2.7.0-40 /Users/<USER>/.gradle/caches/8.10/transforms/f2895f8d4db9723d89c31371ae6a2cfd/transformed/lifecycle-runtime-2.7.0/res
com.voipforwarder.app-drawerlayout-1.1.1-41 /Users/<USER>/.gradle/caches/8.10/transforms/f471d96919ee831700aaa7793cb88db9/transformed/drawerlayout-1.1.1/res
com.voipforwarder.app-jetified-startup-runtime-1.1.1-42 /Users/<USER>/.gradle/caches/8.10/transforms/f6271a2bea491e87ad8153945b8d7eb3/transformed/jetified-startup-runtime-1.1.1/res
com.voipforwarder.app-jetified-linphone-sdk-android-5.2.7-43 /Users/<USER>/.gradle/caches/8.10/transforms/f73d05128628366b7c67fb7e8b7f2a31/transformed/jetified-linphone-sdk-android-5.2.7/res
com.voipforwarder.app-pngs-44 /Users/<USER>/Downloads/project/html/VOIP/Android/app/build/generated/res/pngs/release
com.voipforwarder.app-resValues-45 /Users/<USER>/Downloads/project/html/VOIP/Android/app/build/generated/res/resValues/release
com.voipforwarder.app-packageReleaseResources-46 /Users/<USER>/Downloads/project/html/VOIP/Android/app/build/intermediates/incremental/release/packageReleaseResources/merged.dir
com.voipforwarder.app-packageReleaseResources-47 /Users/<USER>/Downloads/project/html/VOIP/Android/app/build/intermediates/incremental/release/packageReleaseResources/stripped.dir
com.voipforwarder.app-merged_res-48 /Users/<USER>/Downloads/project/html/VOIP/Android/app/build/intermediates/merged_res/release
com.voipforwarder.app-main-49 /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/main/res
com.voipforwarder.app-release-50 /Users/<USER>/Downloads/project/html/VOIP/Android/app/src/release/res
